# AI SIP Call Agent

A self-hosted, local-only AI-powered SIP call agent capable of engaging in conversations with callers to achieve pre-defined goals.

## Features

- **SIP Integration**: Handles incoming and outgoing SIP calls
- **Real-time Speech Processing**: Speech-to-text using Whisper and text-to-speech using Kokoro TTS
- **AI Conversation**: Powered by LiteLLM for intelligent responses
- **Web Interface**: Monitor calls, view transcripts, and control agent behavior
- **Goal-oriented**: Configurable conversation goals and objectives
- **Local Processing**: All processing happens locally for privacy

## Architecture

```
Caller <-> SIP Client <-> Call Agent Service <-> Whisper (STT)
                                              <-> Kokoro (TTS)
                                              <-> Li<PERSON>LL<PERSON> (AI)
                                              <-> Web Interface
```

## Components

1. **Call Agent Service**: Central orchestrator managing all components
2. **SIP Client**: Modified `sip-audio-session3.py` for audio streaming
3. **Speech Processing Pipeline**: Real-time STT and TTS integration
4. **Web Interface**: FastAPI-based monitoring and control interface
5. **Configuration System**: YAML-based configuration with environment overrides

## Prerequisites

- Python 3.8+
- SIP Simple SDK dependencies
- Access to external services:
  - Whisper server at `http://***************:8080`
  - Kokoro TTS server at `http://***************:8081`
  - LiteLLM at `https://litellm.xn--8pr.xyz/`

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Copy and configure environment:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```
4. Configure SIP settings using `sip_settings` script from SIP Simple SDK

## Usage

1. Start the Call Agent Service:
   ```bash
   python -m src.call_agent.main
   ```

2. Access the web interface at `http://localhost:8000`

3. The agent will automatically handle incoming SIP calls

## Configuration

Edit `config.yaml` to customize:
- SIP settings
- External service URLs
- Agent personality and goals
- Audio processing parameters
- Web interface settings

## Directory Structure

```
├── src/
│   ├── call_agent/          # Core call agent service
│   ├── sip_client/          # Modified SIP client
│   ├── audio_processing/    # Audio handling components
│   └── web_interface/       # Web monitoring interface
├── tests/                   # Unit and integration tests
├── logs/                    # Application logs
├── conversations/           # Conversation history
├── transcripts/            # Call transcripts
├── audio_cache/            # Cached audio files
├── recordings/             # Call recordings
├── config.yaml            # Main configuration
└── requirements.txt        # Python dependencies
```

## Development

Run tests:
```bash
pytest tests/
```

Format code:
```bash
black src/ tests/
```

Lint code:
```bash
flake8 src/ tests/
```

## License

MIT License
