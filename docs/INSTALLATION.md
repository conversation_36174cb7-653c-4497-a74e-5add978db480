# Installation Guide

This guide will help you install and set up the AI SIP Call Agent on your system.

## Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows with WSL2
- **Python**: 3.8 or higher
- **Memory**: Minimum 4GB RAM, 8GB+ recommended
- **Storage**: At least 2GB free space
- **Network**: Internet connection for external services

### External Services

The AI SIP Call Agent requires access to three external services:

1. **Whisper STT Server** - Speech-to-text processing
   - Default URL: `http://***************:8080`
   - Must be running whisper.cpp server

2. **Kokoro TTS Server** - Text-to-speech synthesis
   - Default URL: `http://***************:8081`
   - Must support the Kokoro TTS API

3. **LiteLLM Service** - AI language model inference
   - Default URL: `https://litellm.xn--8pr.xyz/`
   - API Key: `sk-1337`

### SIP Infrastructure

- **SIP Account**: You need a valid SIP account configured
- **SIP Simple SDK**: Will be installed automatically
- **Audio Devices**: Microphone and speakers (for testing)

## Installation Steps

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url>
cd ai-sip-call-agent
```

### 2. Create Virtual Environment

```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Install System Dependencies

#### Ubuntu/Debian

```bash
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    python3-dev \
    libasound2-dev \
    portaudio19-dev \
    libssl-dev \
    libffi-dev
```

#### macOS

```bash
brew install portaudio
```

#### Windows (WSL2)

```bash
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    python3-dev \
    libasound2-dev \
    portaudio19-dev
```

### 5. Configure SIP Settings

The application uses the SIP Simple SDK configuration. Set up your SIP account:

```bash
# Install SIP Simple SDK tools
pip install setuptools \
    wheel \
    cython \
    dnspython \
    lxml \
    twisted \
    python-dateutil \
    greenlet \
    zope.interface \
    requests \
    gmpy2 \
    gevent

pip install git+https://github.com/AGProjects/python3-eventlib.git
pip install git+https://github.com/AGProjects/python3-xcaplib.git
pip install git+https://github.com/AGProjects/python3-msrplib.git
pip install git+https://github.com/AGProjects/python3-gnutls.git
pip install git+https://github.com/AGProjects/python3-application.git
pip install git+https://github.com/AGProjects/python3-otr.git

sudo apt install python3 dh-python python3-all-dev cython3 libasound2-dev python3-dateutil python3-dnspython libssl-dev libv4l-dev libavcodec-dev libavformat-dev libopencore-amrnb-dev libopencore-amrwb-dev libavutil-dev libswscale-dev libx264-dev libvpx-dev libopus-dev libsqlite3-dev pkg-config uuid-dev cython3 cython3-dbg python3-setuptools devscripts debhelper dh-python python3-all-dev python3-all-dbg libasound2-dev libssl-dev libsqlite3-dev

git clone https://github.com/AGProjects/python3-sipsimple.git
cd python3-sipsimple/
sh ./get_dependencies.sh
pip install .
# Configure your SIP account
pip install git+https://github.com/AGProjects/sipclients3.git
sip-settings3
```

Follow the prompts to configure:
- SIP account credentials
- Audio devices
- Network settings

### 6. Configure Application

Copy the example environment file:

```bash
cp .env.example .env
```

Edit `.env` to match your setup:

```bash
# External Service URLs
WHISPER_BASE_URL=http://***************:8080
KOKORO_TTS_BASE_URL=http://***************:8081
LITELLM_BASE_URL=https://litellm.xn--8pr.xyz/
LITELLM_API_KEY=sk-1337

# Web Interface
WEB_HOST=0.0.0.0
WEB_PORT=8000

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/call_agent.log
```

Edit `config.yaml` for detailed configuration:

```yaml
# SIP Configuration
sip:
  auto_answer: true
  auto_answer_interval: 2

# Agent Behavior
agent:
  name: "AI Assistant"
  personality: "helpful and professional"
  goals:
    - "Assist callers with their questions"
    - "Gather contact information if appropriate"
    - "Provide helpful information"
```

### 7. Create Required Directories

```bash
mkdir -p logs conversations transcripts audio_cache recordings
```

### 8. Verify External Services

Test connectivity to external services:

```bash
# Test Whisper service
curl http://***************:8080/

# Test TTS service
curl http://***************:8081/

# Test LiteLLM service
curl -H "Authorization: Bearer sk-1337" \
     https://litellm.xn--8pr.xyz/v1/models
```

## Running the Application

### 1. Start the Call Agent Service

```bash
python -m src.call_agent.main
```

### 2. Access the Web Interface

Open your browser and navigate to:
```
http://localhost:8000
```

### 3. Monitor Logs

```bash
tail -f logs/call_agent.log
```

## Testing the Installation

### 1. Run Unit Tests

```bash
pytest tests/
```

### 2. Test SIP Connectivity

```bash
# Test SIP registration
python -c "
from sipsimple.application import SIPApplication
from sipsimple.storage import FileStorage
app = SIPApplication()
app.start(FileStorage())
print('SIP application started successfully')
app.stop()
"
```

### 3. Test External Services

```bash
# Test the complete pipeline
python tests/test_integration.py
```

### 4. Make a Test Call

1. Configure a SIP client to call your agent
2. Monitor the web interface at `http://localhost:8000`
3. Check logs for call processing

## Troubleshooting

### Common Issues

#### 1. SIP Registration Fails

```bash
# Check SIP configuration
sip_settings --list

# Test network connectivity
ping your-sip-provider.com

# Check firewall settings
sudo ufw status
```

#### 2. Audio Issues

```bash
# List audio devices
python -c "
import pyaudio
p = pyaudio.PyAudio()
for i in range(p.get_device_count()):
    print(p.get_device_info_by_index(i))
"

# Test audio recording
arecord -d 5 test.wav
aplay test.wav
```

#### 3. External Service Connection Issues

```bash
# Check network connectivity
curl -v http://***************:8080/
curl -v http://***************:8081/

# Check DNS resolution
nslookup litellm.xn--8pr.xyz

# Test with different timeout
curl --connect-timeout 10 http://***************:8080/
```

#### 4. Permission Issues

```bash
# Fix file permissions
chmod +x src/call_agent/main.py
chown -R $USER:$USER logs/ conversations/ transcripts/

# Audio device permissions (Linux)
sudo usermod -a -G audio $USER
```

#### 5. Python Dependencies

```bash
# Reinstall dependencies
pip install --force-reinstall -r requirements.txt

# Check for conflicts
pip check

# Update pip
pip install --upgrade pip
```

### Log Analysis

Check logs for specific errors:

```bash
# General errors
grep -i error logs/call_agent.log

# SIP issues
grep -i sip logs/call_agent.log

# Audio processing issues
grep -i audio logs/call_agent.log

# External service issues
grep -i "whisper\|tts\|llm" logs/call_agent.log
```

### Performance Tuning

#### 1. Audio Settings

Adjust in `config.yaml`:

```yaml
audio:
  chunk_size: 1024      # Smaller = lower latency, higher CPU
  sample_rate: 16000    # Higher = better quality, more bandwidth
  silence_threshold: 0.01  # Lower = more sensitive
  silence_duration: 2.0    # Shorter = more responsive
```

#### 2. Memory Usage

```yaml
agent:
  max_conversation_turns: 50  # Reduce for lower memory usage
  conversation_timeout: 300   # Shorter timeouts free memory faster
```

#### 3. Service Timeouts

```yaml
services:
  whisper:
    timeout: 30  # Increase if STT is slow
  kokoro_tts:
    timeout: 30  # Increase if TTS is slow
  litellm:
    timeout: 30  # Increase if LLM is slow
```

## Next Steps

1. **Configure Goals**: Customize conversation goals in `config.yaml`
2. **Set Up Monitoring**: Configure log aggregation and monitoring
3. **Scale Deployment**: Consider Docker deployment for production
4. **Backup Configuration**: Back up your SIP and application configuration
5. **Security**: Review security settings for production deployment

For more detailed configuration options, see [CONFIGURATION.md](CONFIGURATION.md).
For deployment in production, see [DEPLOYMENT.md](DEPLOYMENT.md).
