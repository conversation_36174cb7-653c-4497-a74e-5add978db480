# AI SIP Call Agent Configuration

# SIP Configuration
sip:
  account: null  # Will use default account from sipsimple config
  auto_answer: true
  auto_answer_interval: 2  # seconds
  enable_recording: true
  recording_dir: "./recordings"

# External Services
services:
  whisper:
    base_url: "http://***************:8080"
    inference_path: "/inference"
    timeout: 30
    
  kokoro_tts:
    base_url: "http://***************:8081"
    tts_path: "/tts"
    audio_path: "/audio"
    voice: "af_heart"
    speed: 1.0
    timeout: 30
    
  litellm:
    base_url: "https://litellm.xn--8pr.xyz/"
    api_key: "sk-1337"
    model: "ollama/gemma3:4b"
    timeout: 30

# Audio Processing
audio:
  chunk_size: 1024  # bytes
  sample_rate: 16000  # Hz
  channels: 1
  format: "wav"
  silence_threshold: 0.01
  silence_duration: 2.0  # seconds before processing speech
  max_recording_duration: 30.0  # seconds

# Call Agent Behavior
agent:
  name: "AI Assistant"
  personality: "helpful and professional"
  max_conversation_turns: 50
  conversation_timeout: 300  # seconds
  
  # Default system prompt
  system_prompt: |
    You are a helpful AI assistant answering phone calls. 
    Keep responses concise and conversational.
    Ask clarifying questions when needed.
    Be polite and professional.
    
  # Goals and objectives
  goals:
    - "Assist callers with their questions"
    - "Gather contact information if appropriate"
    - "Provide helpful information"
    - "Maintain a friendly conversation"

# Web Interface
web:
  host: "0.0.0.0"
  port: 8000
  debug: true
  
# Logging
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "./logs/call_agent.log"
  max_file_size: "10MB"
  backup_count: 5

# Storage
storage:
  conversations_dir: "./conversations"
  transcripts_dir: "./transcripts"
  audio_cache_dir: "./audio_cache"
  max_cache_size: "1GB"
  cleanup_after_days: 30
