"""Tests for configuration management."""

import pytest
import tempfile
import yaml
from pathlib import Path

from src.call_agent.config import Config, load_config, _merge_configs


def test_default_config():
    """Test default configuration values."""
    config = Config()
    
    assert config.sip.auto_answer is True
    assert config.sip.auto_answer_interval == 2
    assert config.audio.sample_rate == 16000
    assert config.audio.channels == 1
    assert config.agent.name == "AI Assistant"
    assert config.web.port == 8000


def test_config_from_yaml():
    """Test loading configuration from YAML file."""
    config_data = {
        "sip": {
            "auto_answer": False,
            "auto_answer_interval": 5
        },
        "agent": {
            "name": "Test Agent",
            "personality": "test personality"
        }
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(config_data, f)
        config_path = f.name
    
    try:
        config = load_config(config_path)
        
        assert config.sip.auto_answer is False
        assert config.sip.auto_answer_interval == 5
        assert config.agent.name == "Test Agent"
        assert config.agent.personality == "test personality"
        
        # Check that defaults are preserved
        assert config.audio.sample_rate == 16000
        assert config.web.port == 8000
        
    finally:
        Path(config_path).unlink()


def test_merge_configs():
    """Test configuration merging."""
    base = {
        "sip": {
            "auto_answer": True,
            "auto_answer_interval": 2
        },
        "agent": {
            "name": "Base Agent"
        }
    }
    
    override = {
        "sip": {
            "auto_answer": False
        },
        "agent": {
            "personality": "override personality"
        },
        "new_section": {
            "value": "test"
        }
    }
    
    result = _merge_configs(base, override)
    
    assert result["sip"]["auto_answer"] is False
    assert result["sip"]["auto_answer_interval"] == 2  # Preserved from base
    assert result["agent"]["name"] == "Base Agent"  # Preserved from base
    assert result["agent"]["personality"] == "override personality"  # From override
    assert result["new_section"]["value"] == "test"  # New section


def test_config_validation():
    """Test configuration validation."""
    # Test valid configuration
    config = Config(
        sip={"auto_answer": True, "auto_answer_interval": 5},
        audio={"sample_rate": 8000, "channels": 2}
    )
    
    assert config.sip.auto_answer is True
    assert config.sip.auto_answer_interval == 5
    assert config.audio.sample_rate == 8000
    assert config.audio.channels == 2


def test_nested_config_access():
    """Test accessing nested configuration values."""
    config = Config()
    
    # Test nested access
    assert config.services.whisper.base_url == "http://***************:8080"
    assert config.services.kokoro_tts.voice == "af_heart"
    assert config.services.litellm.model == "ollama/gemma3:4b"
    
    # Test deep nesting
    assert config.services.whisper.inference_path == "/inference"
    assert config.services.kokoro_tts.tts_path == "/tts"
