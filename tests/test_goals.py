"""Tests for goal management."""

import pytest
from src.call_agent.goals import Goal, GoalManager, GoalType, GoalStatus


def test_goal_creation():
    """Test creating a goal."""
    goal = Goal(
        id="test_goal",
        name="Test Goal",
        description="A test goal",
        goal_type=GoalType.GENERAL_ASSISTANCE,
        keywords=["test", "goal"],
        success_criteria=["criterion1", "criterion2"]
    )
    
    assert goal.id == "test_goal"
    assert goal.name == "Test Goal"
    assert goal.goal_type == GoalType.GENERAL_ASSISTANCE
    assert goal.status == GoalStatus.NOT_STARTED
    assert goal.progress == 0.0
    assert "test" in goal.keywords
    assert "criterion1" in goal.success_criteria


def test_goal_manager_initialization():
    """Test goal manager initialization."""
    manager = GoalManager()
    
    # Should have default goals loaded
    assert len(manager.default_goals) > 0
    
    # Test loading default goals
    manager.load_default_goals()
    assert len(manager.goals) > 0
    
    # Check specific default goals
    assert manager.get_goal("greeting") is not None
    assert manager.get_goal("understand_intent") is not None


def test_goal_management():
    """Test adding, removing, and retrieving goals."""
    manager = GoalManager()
    
    goal = Goal(
        id="custom_goal",
        name="Custom Goal",
        description="A custom test goal",
        goal_type=GoalType.CUSTOMER_SERVICE,
        priority=3
    )
    
    # Add goal
    manager.add_goal(goal)
    assert manager.get_goal("custom_goal") is not None
    
    # Remove goal
    manager.remove_goal("custom_goal")
    assert manager.get_goal("custom_goal") is None


def test_goal_evaluation():
    """Test goal evaluation based on conversation."""
    manager = GoalManager()
    manager.load_default_goals()
    
    # Test greeting goal evaluation
    caller_text = "Hello there"
    agent_text = "Hello! I'm your AI assistant. How can I help you today?"
    
    achieved_goals = manager.evaluate_conversation_turn(caller_text, agent_text)
    
    # Should achieve greeting goal
    greeting_goal = manager.get_goal("greeting")
    assert greeting_goal.status == GoalStatus.ACHIEVED
    assert "greeting" in achieved_goals


def test_goal_priority_sorting():
    """Test goal sorting by priority."""
    manager = GoalManager()
    
    goal1 = Goal(id="goal1", name="Goal 1", description="Test", 
                goal_type=GoalType.GENERAL_ASSISTANCE, priority=3)
    goal2 = Goal(id="goal2", name="Goal 2", description="Test", 
                goal_type=GoalType.GENERAL_ASSISTANCE, priority=1)
    goal3 = Goal(id="goal3", name="Goal 3", description="Test", 
                goal_type=GoalType.GENERAL_ASSISTANCE, priority=2)
    
    manager.add_goal(goal1)
    manager.add_goal(goal2)
    manager.add_goal(goal3)
    
    sorted_goals = manager.get_all_goals()
    
    assert sorted_goals[0].priority == 1
    assert sorted_goals[1].priority == 2
    assert sorted_goals[2].priority == 3


def test_active_goals_filtering():
    """Test filtering active goals."""
    manager = GoalManager()
    
    goal1 = Goal(id="goal1", name="Goal 1", description="Test", 
                goal_type=GoalType.GENERAL_ASSISTANCE, status=GoalStatus.NOT_STARTED)
    goal2 = Goal(id="goal2", name="Goal 2", description="Test", 
                goal_type=GoalType.GENERAL_ASSISTANCE, status=GoalStatus.ACHIEVED)
    goal3 = Goal(id="goal3", name="Goal 3", description="Test", 
                goal_type=GoalType.GENERAL_ASSISTANCE, status=GoalStatus.IN_PROGRESS)
    
    manager.add_goal(goal1)
    manager.add_goal(goal2)
    manager.add_goal(goal3)
    
    active_goals = manager.get_active_goals()
    
    assert len(active_goals) == 2
    assert goal1 in active_goals
    assert goal3 in active_goals
    assert goal2 not in active_goals


def test_goal_summary():
    """Test goal summary generation."""
    manager = GoalManager()
    
    goal1 = Goal(id="goal1", name="Goal 1", description="Test", 
                goal_type=GoalType.GENERAL_ASSISTANCE, status=GoalStatus.ACHIEVED)
    goal2 = Goal(id="goal2", name="Goal 2", description="Test", 
                goal_type=GoalType.GENERAL_ASSISTANCE, status=GoalStatus.IN_PROGRESS)
    goal3 = Goal(id="goal3", name="Goal 3", description="Test", 
                goal_type=GoalType.GENERAL_ASSISTANCE, status=GoalStatus.NOT_STARTED)
    
    manager.add_goal(goal1)
    manager.add_goal(goal2)
    manager.add_goal(goal3)
    
    summary = manager.get_goal_summary()
    
    assert summary["total_goals"] == 3
    assert summary["achieved_goals"] == 1
    assert summary["in_progress_goals"] == 1
    assert summary["completion_rate"] == 1/3
    assert len(summary["goals"]) == 3


def test_goal_reset():
    """Test resetting all goals."""
    manager = GoalManager()
    
    goal = Goal(id="goal1", name="Goal 1", description="Test", 
               goal_type=GoalType.GENERAL_ASSISTANCE, 
               status=GoalStatus.ACHIEVED, progress=1.0)
    
    manager.add_goal(goal)
    
    # Reset goals
    manager.reset_goals()
    
    # Check that goal is reset
    reset_goal = manager.get_goal("goal1")
    assert reset_goal.status == GoalStatus.NOT_STARTED
    assert reset_goal.progress == 0.0


def test_information_gathering_evaluation():
    """Test evaluation of information gathering goals."""
    manager = GoalManager()
    manager.load_default_goals()
    
    # Test understanding intent
    caller_text = "I need help with my account"
    agent_text = "I understand you need help with your account. What specifically can I help you with?"
    
    achieved_goals = manager.evaluate_conversation_turn(caller_text, agent_text)
    
    intent_goal = manager.get_goal("understand_intent")
    assert intent_goal.status == GoalStatus.ACHIEVED


def test_contact_info_gathering():
    """Test contact information gathering goal."""
    manager = GoalManager()
    manager.load_default_goals()
    
    caller_text = "My name is John Smith and my phone number is 555-1234"
    agent_text = "Thank you, John. I have your name and phone number."
    
    achieved_goals = manager.evaluate_conversation_turn(caller_text, agent_text)
    
    contact_goal = manager.get_goal("gather_contact_info")
    assert contact_goal.status == GoalStatus.ACHIEVED
