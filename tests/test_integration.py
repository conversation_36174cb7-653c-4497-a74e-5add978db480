"""Integration tests for the AI SIP Call Agent."""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from src.call_agent.agent import CallAgentService
from src.call_agent.config import Config


@pytest.fixture
def mock_config():
    """Create a test configuration."""
    config = Config()
    config.agent.conversation_timeout = 60
    config.agent.max_conversation_turns = 10
    return config


@pytest.fixture
def mock_services():
    """Create mock external services."""
    services = MagicMock()
    
    # Mock Whisper
    services.whisper.transcribe_audio = AsyncMock(return_value="Hello, I need help")
    
    # Mock TTS
    services.tts.synthesize_speech = AsyncMock(return_value=b"fake_audio_data")
    
    # Mock LLM
    services.llm.generate_response = AsyncMock(return_value="Hello! How can I help you today?")
    
    # Mock health check
    services.health_check = AsyncMock(return_value={
        "whisper": True,
        "tts": True,
        "llm": True
    })
    
    return services


@pytest.mark.asyncio
async def test_call_agent_startup_shutdown(mock_config):
    """Test call agent service startup and shutdown."""
    agent = CallAgentService(mock_config)
    
    with patch.object(agent.services, 'health_check', new_callable=AsyncMock) as mock_health:
        mock_health.return_value = {"whisper": True, "tts": True, "llm": True}
        
        # Start service
        await agent.start()
        assert agent.is_running is True
        
        # Stop service
        await agent.stop()
        assert agent.is_running is False


@pytest.mark.asyncio
async def test_complete_call_flow(mock_config, mock_services):
    """Test a complete call flow from start to end."""
    agent = CallAgentService(mock_config)
    agent.services = mock_services
    
    # Start the service
    await agent.start()
    
    try:
        # Start a call
        call_id = await agent.start_call("<EMAIL>")
        assert call_id is not None
        assert call_id in agent.active_calls
        
        # Get call status
        status = agent.get_call_status(call_id)
        assert status is not None
        assert status["caller_id"] == "<EMAIL>"
        assert status["is_active"] is True
        
        # Process audio (simulate conversation)
        audio_data = b"fake_audio_chunk"
        
        # Mock the audio processing to trigger the full pipeline
        call_session = agent.active_calls[call_id]
        call_session.audio_buffer = bytearray(audio_data * 100)  # Simulate enough audio
        
        response_audio = await agent.process_audio(call_id, audio_data)
        
        # Verify services were called
        mock_services.whisper.transcribe_audio.assert_called()
        mock_services.llm.generate_response.assert_called()
        mock_services.tts.synthesize_speech.assert_called()
        
        # End the call
        await agent.end_call(call_id)
        assert call_id not in agent.active_calls
        
    finally:
        await agent.stop()


@pytest.mark.asyncio
async def test_multiple_concurrent_calls(mock_config, mock_services):
    """Test handling multiple concurrent calls."""
    agent = CallAgentService(mock_config)
    agent.services = mock_services
    
    await agent.start()
    
    try:
        # Start multiple calls
        call_ids = []
        for i in range(3):
            call_id = await agent.start_call(f"caller{i}@example.com")
            call_ids.append(call_id)
        
        # Verify all calls are active
        assert len(agent.active_calls) == 3
        
        # Get status of all calls
        all_status = agent.get_all_calls_status()
        assert len(all_status) == 3
        
        # End all calls
        for call_id in call_ids:
            await agent.end_call(call_id)
        
        assert len(agent.active_calls) == 0
        
    finally:
        await agent.stop()


@pytest.mark.asyncio
async def test_goal_achievement_flow(mock_config, mock_services):
    """Test goal achievement during conversation."""
    agent = CallAgentService(mock_config)
    agent.services = mock_services
    
    # Mock LLM to return greeting response
    mock_services.llm.generate_response = AsyncMock(
        return_value="Hello! I'm your AI assistant. How can I help you today?"
    )
    
    await agent.start()
    
    try:
        call_id = await agent.start_call("<EMAIL>")
        call_session = agent.active_calls[call_id]
        
        # Simulate greeting conversation
        mock_services.whisper.transcribe_audio = AsyncMock(return_value="Hello there")
        
        # Process audio to trigger conversation
        call_session.audio_buffer = bytearray(b"fake_audio" * 100)
        await agent.process_audio(call_id, b"fake_audio")
        
        # Check that greeting goal was achieved
        conversation = call_session.conversation
        assert "greeting" in conversation.metadata.goals_achieved
        
        await agent.end_call(call_id)
        
    finally:
        await agent.stop()


@pytest.mark.asyncio
async def test_error_handling(mock_config, mock_services):
    """Test error handling in various scenarios."""
    agent = CallAgentService(mock_config)
    agent.services = mock_services
    
    await agent.start()
    
    try:
        call_id = await agent.start_call("<EMAIL>")
        
        # Test Whisper failure
        mock_services.whisper.transcribe_audio = AsyncMock(return_value=None)
        
        call_session = agent.active_calls[call_id]
        call_session.audio_buffer = bytearray(b"fake_audio" * 100)
        
        response = await agent.process_audio(call_id, b"fake_audio")
        # Should handle gracefully when transcription fails
        
        # Test LLM failure
        mock_services.whisper.transcribe_audio = AsyncMock(return_value="Hello")
        mock_services.llm.generate_response = AsyncMock(return_value=None)
        
        call_session.audio_buffer = bytearray(b"fake_audio" * 100)
        response = await agent.process_audio(call_id, b"fake_audio")
        
        # Should use fallback response
        mock_services.tts.synthesize_speech.assert_called()
        
        await agent.end_call(call_id)
        
    finally:
        await agent.stop()


@pytest.mark.asyncio
async def test_conversation_context_management(mock_config, mock_services):
    """Test conversation context management across multiple turns."""
    agent = CallAgentService(mock_config)
    agent.services = mock_services
    
    await agent.start()
    
    try:
        call_id = await agent.start_call("<EMAIL>")
        call_session = agent.active_calls[call_id]
        
        # Simulate multiple conversation turns
        turns = [
            ("Hello", "Hello! How can I help you?"),
            ("I need help with my account", "I'd be happy to help with your account. What specifically do you need?"),
            ("I forgot my password", "I can help you reset your password. Let me guide you through the process.")
        ]
        
        for caller_text, expected_agent_response in turns:
            mock_services.whisper.transcribe_audio = AsyncMock(return_value=caller_text)
            mock_services.llm.generate_response = AsyncMock(return_value=expected_agent_response)
            
            call_session.audio_buffer = bytearray(b"fake_audio" * 100)
            await agent.process_audio(call_id, b"fake_audio")
        
        # Verify conversation has all turns
        conversation = call_session.conversation
        assert conversation.metadata.total_turns == 6  # 3 caller + 3 agent turns
        
        # Verify context messages include conversation history
        context = conversation.get_context_messages()
        assert len(context) > 1  # System message + conversation turns
        
        await agent.end_call(call_id)
        
    finally:
        await agent.stop()


@pytest.mark.asyncio
async def test_service_health_monitoring(mock_config):
    """Test service health monitoring."""
    agent = CallAgentService(mock_config)
    
    # Test healthy services
    with patch.object(agent.services, 'health_check', new_callable=AsyncMock) as mock_health:
        mock_health.return_value = {"whisper": True, "tts": True, "llm": True}
        
        health = await agent.services.health_check()
        assert all(health.values())
    
    # Test unhealthy services
    with patch.object(agent.services, 'health_check', new_callable=AsyncMock) as mock_health:
        mock_health.return_value = {"whisper": False, "tts": True, "llm": False}
        
        health = await agent.services.health_check()
        assert not all(health.values())
        assert health["tts"] is True


@pytest.mark.asyncio
async def test_call_timeout_handling(mock_config, mock_services):
    """Test call timeout handling."""
    # Set very short timeout for testing
    mock_config.agent.conversation_timeout = 1
    
    agent = CallAgentService(mock_config)
    agent.services = mock_services
    
    await agent.start()
    
    try:
        call_id = await agent.start_call("<EMAIL>")
        call_session = agent.active_calls[call_id]
        
        # Wait for timeout
        await asyncio.sleep(1.1)
        
        # Check if conversation should end due to timeout
        assert call_session.conversation.is_timeout()
        assert call_session.conversation.should_end()
        
        await agent.end_call(call_id, "timeout")
        
    finally:
        await agent.stop()


if __name__ == "__main__":
    pytest.main([__file__])
