"""Tests for conversation management."""

import pytest
from datetime import datetime, timezone
from src.call_agent.conversation import Conversation, ConversationManager, ConversationMetadata
from src.call_agent.config import Config


def test_conversation_creation():
    """Test creating a new conversation."""
    metadata = ConversationMetadata(
        conversation_id="test-123",
        caller_id="<EMAIL>",
        start_time=datetime.now(timezone.utc)
    )
    
    conversation = Conversation(metadata)
    
    assert conversation.metadata.conversation_id == "test-123"
    assert conversation.metadata.caller_id == "<EMAIL>"
    assert conversation.is_active is True
    assert len(conversation.turns) == 0
    assert len(conversation.context_messages) == 1  # System message


def test_conversation_turns():
    """Test adding turns to a conversation."""
    metadata = ConversationMetadata(
        conversation_id="test-123",
        caller_id="<EMAIL>",
        start_time=datetime.now(timezone.utc)
    )
    
    conversation = Conversation(metadata)
    
    # Add caller turn
    caller_turn = conversation.add_turn("caller", "Hello, I need help")
    assert caller_turn.speaker == "caller"
    assert caller_turn.content == "Hello, I need help"
    assert conversation.metadata.total_turns == 1
    
    # Add agent turn
    agent_turn = conversation.add_turn("agent", "Hello! How can I help you?", processing_time=1.5)
    assert agent_turn.speaker == "agent"
    assert agent_turn.processing_time == 1.5
    assert conversation.metadata.total_turns == 2
    
    # Check context messages
    context = conversation.get_context_messages()
    assert len(context) == 3  # System + 2 turns
    assert context[1]["role"] == "user"
    assert context[1]["content"] == "Hello, I need help"
    assert context[2]["role"] == "assistant"
    assert context[2]["content"] == "Hello! How can I help you?"


def test_conversation_context_trimming():
    """Test conversation context trimming."""
    config = Config()
    config.agent.max_conversation_turns = 3
    
    metadata = ConversationMetadata(
        conversation_id="test-123",
        caller_id="<EMAIL>",
        start_time=datetime.now(timezone.utc)
    )
    
    conversation = Conversation(metadata, config)
    
    # Add many turns
    for i in range(5):
        conversation.add_turn("caller", f"Message {i}")
        conversation.add_turn("agent", f"Response {i}")
    
    # Should keep system message + last 3 turns
    context = conversation.get_context_messages()
    assert len(context) == 4  # System + 3 recent messages
    assert context[0]["role"] == "system"
    assert "Response 4" in context[-1]["content"]


def test_conversation_transcript():
    """Test generating conversation transcript."""
    metadata = ConversationMetadata(
        conversation_id="test-123",
        caller_id="<EMAIL>",
        start_time=datetime.now(timezone.utc)
    )
    
    conversation = Conversation(metadata)
    conversation.add_turn("caller", "Hello")
    conversation.add_turn("agent", "Hi there!")
    
    transcript = conversation.get_transcript()
    
    assert "test-123" in transcript
    assert "<EMAIL>" in transcript
    assert "CALLER: Hello" in transcript
    assert "AGENT: Hi there!" in transcript


def test_conversation_timeout():
    """Test conversation timeout detection."""
    config = Config()
    config.agent.conversation_timeout = 1  # 1 second timeout
    
    metadata = ConversationMetadata(
        conversation_id="test-123",
        caller_id="<EMAIL>",
        start_time=datetime.now(timezone.utc)
    )
    
    conversation = Conversation(metadata, config)
    
    # Should not be timed out initially
    assert not conversation.is_timeout()
    
    # Simulate time passing (in real test, you'd use time mocking)
    import time
    time.sleep(1.1)
    
    # Should be timed out now
    assert conversation.is_timeout()


def test_conversation_manager():
    """Test conversation manager functionality."""
    manager = ConversationManager()
    
    # Create conversation
    conversation = manager.create_conversation("<EMAIL>")
    
    assert conversation.metadata.caller_id == "<EMAIL>"
    assert conversation.metadata.conversation_id in manager.conversations
    
    # Get conversation
    retrieved = manager.get_conversation(conversation.metadata.conversation_id)
    assert retrieved is conversation
    
    # End conversation
    manager.end_conversation(conversation.metadata.conversation_id)
    assert conversation.metadata.conversation_id not in manager.conversations
    assert not conversation.is_active


def test_conversation_goal_tracking():
    """Test goal achievement tracking in conversations."""
    metadata = ConversationMetadata(
        conversation_id="test-123",
        caller_id="<EMAIL>",
        start_time=datetime.now(timezone.utc)
    )
    
    conversation = Conversation(metadata)
    
    # Mark goals as achieved
    conversation.mark_goal_achieved("greeting")
    conversation.mark_goal_achieved("understand_intent")
    
    assert "greeting" in conversation.metadata.goals_achieved
    assert "understand_intent" in conversation.metadata.goals_achieved
    assert len(conversation.metadata.goals_achieved) == 2
    
    # Don't duplicate goals
    conversation.mark_goal_achieved("greeting")
    assert len(conversation.metadata.goals_achieved) == 2


def test_conversation_summary():
    """Test conversation summary generation."""
    metadata = ConversationMetadata(
        conversation_id="test-123",
        caller_id="<EMAIL>",
        start_time=datetime.now(timezone.utc)
    )
    
    conversation = Conversation(metadata)
    conversation.add_turn("caller", "Hello")
    conversation.add_turn("agent", "Hi there!")
    conversation.mark_goal_achieved("greeting")
    
    summary = conversation.get_summary()
    
    assert summary["conversation_id"] == "test-123"
    assert summary["caller_id"] == "<EMAIL>"
    assert summary["total_turns"] == 2
    assert summary["goals_achieved"] == ["greeting"]
    assert summary["is_active"] is True
    assert "start_time" in summary


def test_conversation_ending():
    """Test conversation ending."""
    metadata = ConversationMetadata(
        conversation_id="test-123",
        caller_id="<EMAIL>",
        start_time=datetime.now(timezone.utc)
    )
    
    conversation = Conversation(metadata)
    
    # End conversation
    conversation.end("completed")
    
    assert not conversation.is_active
    assert conversation.metadata.status == "completed"
    assert conversation.metadata.end_time is not None


def test_conversation_should_end():
    """Test conversation end conditions."""
    config = Config()
    config.agent.max_conversation_turns = 4  # 2 caller + 2 agent turns
    config.agent.conversation_timeout = 1
    
    metadata = ConversationMetadata(
        conversation_id="test-123",
        caller_id="<EMAIL>",
        start_time=datetime.now(timezone.utc)
    )
    
    conversation = Conversation(metadata, config)
    
    # Should not end initially
    assert not conversation.should_end()
    
    # Add turns up to limit
    conversation.add_turn("caller", "Hello")
    conversation.add_turn("agent", "Hi")
    conversation.add_turn("caller", "How are you?")
    conversation.add_turn("agent", "I'm good")
    
    # Should end due to turn limit
    assert conversation.should_end()
