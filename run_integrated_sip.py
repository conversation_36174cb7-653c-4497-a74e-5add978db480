#!/usr/bin/env python3
"""
Complete AI SIP Integration Runner
Starts both the AI backend and the integrated SIP client.
"""

import sys
import subprocess
import time
import signal
import threading
from pathlib import Path


def run_ai_backend():
    """Run the AI backend in a subprocess."""
    print("🚀 Starting AI Backend...")
    
    backend_process = subprocess.Popen([
        sys.executable, "ai_backend.py"
    ], cwd=str(Path(__file__).parent))
    
    return backend_process


def run_sip_client():
    """Run the integrated SIP client."""
    print("📞 Starting Integrated SIP Client...")
    
    # Wait a bit for the backend to start
    time.sleep(5)
    
    sip_process = subprocess.Popen([
        sys.executable, "src/sip_client/integrated_client.py"
    ], cwd=str(Path(__file__).parent))
    
    return sip_process


def main():
    """Main entry point."""
    print("🎯 AI SIP Call Agent - Complete Integration")
    print("=" * 60)
    print("🤖 This will start:")
    print("   1. AI Backend (web dashboard, STT, TTS, LLM)")
    print("   2. Integrated SIP Client (auto-accepts calls)")
    print("   3. Real-time audio processing pipeline")
    print()
    print("🌐 Web Dashboard: http://localhost:8000")
    print("📞 SIP calls will be auto-accepted and processed")
    print("=" * 60)
    
    backend_process = None
    sip_process = None
    
    try:
        # Start AI backend
        backend_process = run_ai_backend()
        
        # Start SIP client
        sip_process = run_sip_client()
        
        print()
        print("🎉 Both services are starting...")
        print("🔍 Check the output above for any errors")
        print("🌐 Visit http://localhost:8000 to see the dashboard")
        print("📞 Make a test call to see the integration in action!")
        print()
        print("🛑 Press Ctrl+C to stop both services")
        
        # Wait for processes
        while True:
            # Check if processes are still running
            if backend_process.poll() is not None:
                print("❌ AI Backend stopped unexpectedly")
                break
            if sip_process.poll() is not None:
                print("❌ SIP Client stopped unexpectedly")
                break
                
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Shutting down services...")
        
        # Terminate processes gracefully
        if sip_process:
            print("📞 Stopping SIP Client...")
            sip_process.terminate()
            try:
                sip_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                sip_process.kill()
                
        if backend_process:
            print("🤖 Stopping AI Backend...")
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend_process.kill()
                
        print("✅ All services stopped")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
        # Clean up processes
        for process in [sip_process, backend_process]:
            if process:
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except:
                    try:
                        process.kill()
                    except:
                        pass
                        
    finally:
        print("👋 Goodbye!")


if __name__ == "__main__":
    main()
