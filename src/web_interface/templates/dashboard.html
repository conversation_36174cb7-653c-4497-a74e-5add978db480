<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .status-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #667eea;
        }
        
        .status-card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .status-value {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-healthy { background-color: #10b981; }
        .status-unhealthy { background-color: #ef4444; }
        .status-unknown { background-color: #f59e0b; }
        
        .calls-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .calls-header {
            background: #f8fafc;
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .calls-header h2 {
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        
        .calls-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .call-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            transition: background-color 0.2s;
        }
        
        .call-item:hover {
            background-color: #f8fafc;
        }
        
        .call-item:last-child {
            border-bottom: none;
        }
        
        .call-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .call-id {
            font-weight: bold;
            color: #667eea;
        }
        
        .call-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .call-active {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .call-processing {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .call-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            font-size: 0.875rem;
            color: #64748b;
        }
        
        .call-actions {
            margin-top: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: background-color 0.2s;
        }
        
        .btn-danger {
            background-color: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #dc2626;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #64748b;
        }
        
        .empty-state h3 {
            margin-bottom: 0.5rem;
            color: #94a3b8;
        }
        
        .transcript-section {
            margin-top: 2rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .transcript-header {
            background: #f8fafc;
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .transcript-content {
            padding: 1.5rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .transcript-item {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .transcript-caller {
            background: #e0f2fe;
            border-left: 4px solid #0284c7;
        }
        
        .transcript-agent {
            background: #f0fdf4;
            border-left: 4px solid #16a34a;
        }
        
        .transcript-meta {
            font-size: 0.75rem;
            color: #64748b;
            margin-bottom: 0.5rem;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .connected {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .disconnected {
            background-color: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">
        <span class="status-indicator status-unknown"></span>
        Connecting...
    </div>
    
    <div class="container">
        <div class="header">
            <h1>AI SIP Call Agent</h1>
            <p>Real-time monitoring and control dashboard</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h3>System Status</h3>
                <div id="systemStatus">
                    <div class="loading"></div>
                </div>
            </div>
            
            <div class="status-card">
                <h3>Active Calls</h3>
                <div class="status-value" id="activeCallsCount">0</div>
            </div>
            
            <div class="status-card">
                <h3>Service Health</h3>
                <div id="serviceHealth">
                    <div class="loading"></div>
                </div>
            </div>
            
            <div class="status-card">
                <h3>Uptime</h3>
                <div class="status-value" id="uptime">--:--:--</div>
            </div>
        </div>
        
        <div class="calls-section">
            <div class="calls-header">
                <h2>Active Calls</h2>
                <p>Real-time call monitoring and management</p>
            </div>
            <div class="calls-list" id="callsList">
                <div class="empty-state">
                    <h3>No active calls</h3>
                    <p>Waiting for incoming calls...</p>
                </div>
            </div>
        </div>
        
        <div class="transcript-section">
            <div class="transcript-header">
                <h2>Live Transcript</h2>
                <p>Real-time conversation transcripts</p>
            </div>
            <div class="transcript-content" id="transcriptContent">
                <div class="empty-state">
                    <h3>No transcripts</h3>
                    <p>Transcripts will appear here during active calls</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // WebSocket connection for real-time updates
        let ws = null;
        let startTime = Date.now();
        
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                console.log('WebSocket connected');
                updateConnectionStatus(true);
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                handleWebSocketMessage(message);
            };
            
            ws.onclose = function() {
                console.log('WebSocket disconnected');
                updateConnectionStatus(false);
                // Reconnect after 3 seconds
                setTimeout(connectWebSocket, 3000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
                updateConnectionStatus(false);
            };
        }
        
        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connectionStatus');
            const indicator = statusEl.querySelector('.status-indicator');
            
            if (connected) {
                statusEl.className = 'connection-status connected';
                indicator.className = 'status-indicator status-healthy';
                statusEl.innerHTML = '<span class="status-indicator status-healthy"></span>Connected';
            } else {
                statusEl.className = 'connection-status disconnected';
                indicator.className = 'status-indicator status-unhealthy';
                statusEl.innerHTML = '<span class="status-indicator status-unhealthy"></span>Disconnected';
            }
        }
        
        function handleWebSocketMessage(message) {
            switch (message.type) {
                case 'initial_status':
                    updateDashboard(message.data);
                    break;
                case 'call_started':
                    addCallToList(message.data);
                    updateActiveCallsCount();
                    break;
                case 'call_ended':
                    removeCallFromList(message.data.call_id);
                    updateActiveCallsCount();
                    break;
                case 'transcript_update':
                    addTranscriptItem(message.data);
                    break;
                case 'status_update':
                    updateCallsList(message.data);
                    break;
            }
        }
        
        function updateDashboard(data) {
            updateCallsList(data.active_calls);
            updateActiveCallsCount();
        }
        
        function updateCallsList(calls) {
            const callsList = document.getElementById('callsList');
            
            if (Object.keys(calls).length === 0) {
                callsList.innerHTML = `
                    <div class="empty-state">
                        <h3>No active calls</h3>
                        <p>Waiting for incoming calls...</p>
                    </div>
                `;
                return;
            }
            
            callsList.innerHTML = '';
            for (const [callId, call] of Object.entries(calls)) {
                addCallToList(call);
            }
        }
        
        function addCallToList(call) {
            const callsList = document.getElementById('callsList');
            
            // Remove empty state if present
            const emptyState = callsList.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }
            
            const callItem = document.createElement('div');
            callItem.className = 'call-item';
            callItem.id = `call-${call.call_id}`;
            
            const statusClass = call.is_processing ? 'call-processing' : 'call-active';
            const statusText = call.is_processing ? 'Processing' : 'Active';
            
            callItem.innerHTML = `
                <div class="call-header">
                    <span class="call-id">${call.call_id.substring(0, 8)}...</span>
                    <span class="call-status ${statusClass}">${statusText}</span>
                </div>
                <div class="call-details">
                    <div><strong>Caller:</strong> ${call.caller_id}</div>
                    <div><strong>Duration:</strong> ${formatDuration(call.duration_seconds)}</div>
                    <div><strong>Turns:</strong> ${call.total_turns}</div>
                    <div><strong>Goals:</strong> ${call.goals_achieved.length}</div>
                </div>
                <div class="call-actions">
                    <button class="btn btn-danger" onclick="endCall('${call.call_id}')">End Call</button>
                </div>
            `;
            
            callsList.appendChild(callItem);
        }
        
        function removeCallFromList(callId) {
            const callItem = document.getElementById(`call-${callId}`);
            if (callItem) {
                callItem.remove();
            }
            
            // Add empty state if no calls left
            const callsList = document.getElementById('callsList');
            if (callsList.children.length === 0) {
                callsList.innerHTML = `
                    <div class="empty-state">
                        <h3>No active calls</h3>
                        <p>Waiting for incoming calls...</p>
                    </div>
                `;
            }
        }
        
        function updateActiveCallsCount() {
            const callItems = document.querySelectorAll('.call-item:not(.empty-state)');
            document.getElementById('activeCallsCount').textContent = callItems.length;
        }
        
        function addTranscriptItem(transcript) {
            const transcriptContent = document.getElementById('transcriptContent');
            
            // Remove empty state if present
            const emptyState = transcriptContent.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }
            
            // Add caller message
            const callerItem = document.createElement('div');
            callerItem.className = 'transcript-item transcript-caller';
            callerItem.innerHTML = `
                <div class="transcript-meta">Caller • ${new Date().toLocaleTimeString()}</div>
                <div>${transcript.caller_text}</div>
            `;
            transcriptContent.appendChild(callerItem);
            
            // Add agent response
            const agentItem = document.createElement('div');
            agentItem.className = 'transcript-item transcript-agent';
            agentItem.innerHTML = `
                <div class="transcript-meta">Agent • ${new Date().toLocaleTimeString()} • ${transcript.processing_time.toFixed(2)}s</div>
                <div>${transcript.agent_text}</div>
            `;
            transcriptContent.appendChild(agentItem);
            
            // Scroll to bottom
            transcriptContent.scrollTop = transcriptContent.scrollHeight;
        }
        
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            
            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
        
        function updateUptime() {
            const elapsed = Date.now() - startTime;
            const seconds = Math.floor(elapsed / 1000);
            document.getElementById('uptime').textContent = formatDuration(seconds);
        }
        
        async function endCall(callId) {
            try {
                const response = await fetch(`/api/calls/${callId}/end`, {
                    method: 'POST'
                });
                
                if (!response.ok) {
                    throw new Error('Failed to end call');
                }
                
                console.log(`Call ${callId} ended successfully`);
            } catch (error) {
                console.error('Error ending call:', error);
                alert('Failed to end call');
            }
        }
        
        async function loadSystemStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();
                
                // Update system status
                const systemStatusEl = document.getElementById('systemStatus');
                const healthIndicator = status.is_running ? 'status-healthy' : 'status-unhealthy';
                const healthText = status.is_running ? 'Running' : 'Stopped';
                systemStatusEl.innerHTML = `
                    <span class="status-indicator ${healthIndicator}"></span>
                    ${healthText}
                `;
                
                // Update service health
                const serviceHealthEl = document.getElementById('serviceHealth');
                const services = status.service_health;
                const allHealthy = Object.values(services).every(h => h);
                const healthClass = allHealthy ? 'status-healthy' : 'status-unhealthy';
                
                serviceHealthEl.innerHTML = `
                    <div><span class="status-indicator ${healthClass}"></span>Services</div>
                    <div style="font-size: 0.75rem; margin-top: 0.5rem;">
                        Whisper: ${services.whisper ? '✓' : '✗'} |
                        TTS: ${services.tts ? '✓' : '✗'} |
                        LLM: ${services.llm ? '✓' : '✗'}
                    </div>
                `;
                
            } catch (error) {
                console.error('Error loading system status:', error);
            }
        }
        
        // Initialize
        connectWebSocket();
        loadSystemStatus();
        
        // Update uptime every second
        setInterval(updateUptime, 1000);
        
        // Refresh system status every 30 seconds
        setInterval(loadSystemStatus, 30000);
    </script>
</body>
</html>
