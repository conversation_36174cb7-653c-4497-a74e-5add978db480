"""FastAPI web interface for the AI SIP Call Agent."""

import asyncio
import logging
from typing import Dict, List, Optional
from datetime import datetime
from pathlib import Path

from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException, Request, File, UploadFile
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.responses import HTMLResponse, JSONResponse, Response
from pydantic import BaseModel

from ..call_agent.agent import CallAgentService
from ..call_agent.config import get_config

logger = logging.getLogger(__name__)


class CallStatus(BaseModel):
    """Call status model for API responses."""
    call_id: str
    caller_id: str
    conversation_id: str
    start_time: str
    duration_seconds: float
    is_active: bool
    is_processing: bool
    total_turns: int
    goals_achieved: List[str]


class TranscriptUpdate(BaseModel):
    """Transcript update model for WebSocket messages."""
    call_id: str
    caller_text: str
    agent_text: str
    processing_time: float
    timestamp: str


class WebSocketManager:
    """Manages WebSocket connections for real-time updates."""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        
    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
        
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")
        
    async def broadcast(self, message: dict):
        """Broadcast a message to all connected clients."""
        if not self.active_connections:
            return
            
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_json(message)
            except Exception as e:
                logger.warning(f"Failed to send message to WebSocket: {e}")
                disconnected.append(connection)
                
        # Remove disconnected connections
        for connection in disconnected:
            self.disconnect(connection)


def create_web_app(call_agent: CallAgentService) -> FastAPI:
    """Create and configure the FastAPI web application."""
    
    app = FastAPI(
        title="AI SIP Call Agent",
        description="Web interface for monitoring and controlling the AI SIP Call Agent",
        version="1.0.0"
    )
    
    config = get_config()
    
    # WebSocket manager for real-time updates
    websocket_manager = WebSocketManager()
    
    # Setup templates and static files
    templates_dir = Path(__file__).parent / "templates"
    static_dir = Path(__file__).parent / "static"
    
    templates_dir.mkdir(exist_ok=True)
    static_dir.mkdir(exist_ok=True)
    
    templates = Jinja2Templates(directory=str(templates_dir))
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    
    # Setup call agent callbacks for real-time updates
    call_agent.on_call_started = lambda status: asyncio.create_task(
        websocket_manager.broadcast({"type": "call_started", "data": status})
    )
    call_agent.on_call_ended = lambda status: asyncio.create_task(
        websocket_manager.broadcast({"type": "call_ended", "data": status})
    )
    call_agent.on_transcript_update = lambda update: asyncio.create_task(
        websocket_manager.broadcast({"type": "transcript_update", "data": update})
    )
    
    @app.get("/", response_class=HTMLResponse)
    async def dashboard(request: Request):
        """Main dashboard page."""
        return templates.TemplateResponse("dashboard.html", {
            "request": request,
            "title": "AI SIP Call Agent Dashboard"
        })
    
    @app.get("/api/status")
    async def get_system_status():
        """Get overall system status."""
        # Check service health
        health = await call_agent.services.health_check()
        
        # Get active calls
        active_calls = call_agent.get_all_calls_status()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "is_running": call_agent.is_running,
            "service_health": health,
            "active_calls_count": len(active_calls),
            "active_calls": active_calls
        }
    
    @app.get("/api/calls", response_model=Dict[str, CallStatus])
    async def get_all_calls():
        """Get status of all active calls."""
        calls = call_agent.get_all_calls_status()
        return {
            call_id: CallStatus(**status) 
            for call_id, status in calls.items()
        }
    
    @app.get("/api/calls/{call_id}", response_model=CallStatus)
    async def get_call_status(call_id: str):
        """Get status of a specific call."""
        status = call_agent.get_call_status(call_id)
        if not status:
            raise HTTPException(status_code=404, detail="Call not found")
        return CallStatus(**status)
    
    @app.post("/api/calls/{call_id}/end")
    async def end_call(call_id: str):
        """End a specific call."""
        status = call_agent.get_call_status(call_id)
        if not status:
            raise HTTPException(status_code=404, detail="Call not found")

        await call_agent.end_call(call_id, "manual_termination")
        return {"message": f"Call {call_id} ended successfully"}

    @app.post("/api/calls/start")
    async def start_call(request: dict):
        """Start a new call session (for SIP client integration)."""
        caller_id = request.get("caller_id")
        if not caller_id:
            raise HTTPException(status_code=400, detail="caller_id is required")

        call_id = await call_agent.start_call(caller_id)
        return {"call_id": call_id, "message": "Call started successfully"}

    @app.post("/api/calls/{call_id}/process_audio")
    async def process_audio(call_id: str, audio: UploadFile = File(...)):
        """Process audio for a specific call."""
        status = call_agent.get_call_status(call_id)
        if not status:
            raise HTTPException(status_code=404, detail="Call not found")

        # Read audio data
        audio_data = await audio.read()
        if not audio_data:
            raise HTTPException(status_code=400, detail="Audio data is required")

        try:
            response_audio = await call_agent.process_audio(call_id, audio_data)

            if response_audio:
                return Response(content=response_audio, media_type="audio/wav")
            else:
                # Return empty response for silence/no transcription
                return Response(content=b"", media_type="audio/wav", status_code=204)
        except Exception as e:
            logger.error(f"Audio processing error for call {call_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Audio processing failed: {str(e)}")
    
    @app.get("/api/config")
    async def get_configuration():
        """Get current configuration."""
        return {
            "agent": {
                "name": config.agent.name,
                "personality": config.agent.personality,
                "goals": config.agent.goals,
                "max_conversation_turns": config.agent.max_conversation_turns,
                "conversation_timeout": config.agent.conversation_timeout
            },
            "audio": {
                "sample_rate": config.audio.sample_rate,
                "channels": config.audio.channels,
                "chunk_size": config.audio.chunk_size,
                "silence_threshold": config.audio.silence_threshold,
                "silence_duration": config.audio.silence_duration
            },
            "services": {
                "whisper_url": config.services.whisper.base_url,
                "tts_url": config.services.kokoro_tts.base_url,
                "llm_url": config.services.litellm.base_url,
                "llm_model": config.services.litellm.model
            }
        }
    
    @app.get("/api/health")
    async def health_check():
        """Health check endpoint."""
        health = await call_agent.services.health_check()
        
        all_healthy = all(health.values())
        status_code = 200 if all_healthy else 503
        
        return JSONResponse(
            status_code=status_code,
            content={
                "status": "healthy" if all_healthy else "unhealthy",
                "services": health,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    @app.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        """WebSocket endpoint for real-time updates."""
        await websocket_manager.connect(websocket)
        
        try:
            # Send initial status
            status = {
                "type": "initial_status",
                "data": {
                    "active_calls": call_agent.get_all_calls_status(),
                    "timestamp": datetime.now().isoformat()
                }
            }
            await websocket.send_json(status)
            
            # Keep connection alive and handle incoming messages
            while True:
                try:
                    # Wait for messages from client
                    data = await websocket.receive_json()
                    
                    # Handle different message types
                    if data.get("type") == "ping":
                        await websocket.send_json({"type": "pong"})
                    elif data.get("type") == "get_status":
                        status = {
                            "type": "status_update",
                            "data": call_agent.get_all_calls_status()
                        }
                        await websocket.send_json(status)
                        
                except Exception as e:
                    logger.error(f"Error handling WebSocket message: {e}")
                    break
                    
        except WebSocketDisconnect:
            pass
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            websocket_manager.disconnect(websocket)
    
    @app.on_event("startup")
    async def startup_event():
        """Application startup event."""
        logger.info("Web interface starting up...")
        
    @app.on_event("shutdown")
    async def shutdown_event():
        """Application shutdown event."""
        logger.info("Web interface shutting down...")
        
        # Close all WebSocket connections
        for connection in websocket_manager.active_connections.copy():
            try:
                await connection.close()
            except:
                pass
    
    return app
