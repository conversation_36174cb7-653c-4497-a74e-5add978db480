#!/usr/bin/env python3

"""
AI-integrated SIP audio session based on the working sip-audio-session3.py
This version properly registers with SIP servers and integrates AI processing.
"""

import os
import sys
import uuid
import asyncio
import threading
import time
from collections import deque
from threading import Thread, Event

from application import log
from application.notification import Notification<PERSON>enter, IObserver
from application.python import Null
from eventlib import api, coros, proc
from sipsimple.application import SIPApplication, SIPApplicationError
from sipsimple.storage import FileStorage
from sipsimple.session import Session
from sipsimple.account import AccountManager
from sipsimple.configuration import ConfigurationError, ConfigurationManager
from sipsimple.core import SIPURI, ToHeader
from sipsimple.lookup import DNSLookup, DNSLookupError
from sipsimple.streams import MediaStreamRegistry
from sipsimple.streams.rtp.audio import AudioStream
from sipsimple.threading.green import run_in_green_thread
from twisted.internet import reactor
from zope.interface import implementer

# Import our AI components
from ..call_agent.agent import CallAgentService
from ..call_agent.config import get_config


@implementer(IObserver)
class AIAudioSession(object):
    """AI-integrated audio session that handles SIP calls with AI processing."""
    
    def __init__(self, call_agent_service):
        self.call_agent = call_agent_service
        self.session = None
        self.audio_stream = None
        self.active_call_id = None
        
        # Audio processing
        self.audio_buffer = deque(maxlen=1000)  # Buffer for incoming audio
        self.processing_thread = None
        self.stop_processing = Event()
        
        # Register for notifications
        notification_center = NotificationCenter()
        notification_center.add_observer(self, name='SIPSessionNewIncoming')
        notification_center.add_observer(self, name='SIPSessionDidStart')
        notification_center.add_observer(self, name='SIPSessionDidEnd')
        notification_center.add_observer(self, name='SIPSessionDidFail')
        notification_center.add_observer(self, name='MediaStreamDidStart')
        notification_center.add_observer(self, name='MediaStreamDidEnd')
        notification_center.add_observer(self, name='AudioStreamDidReceiveRTP')
        
    def handle_notification(self, notification):
        """Handle SIP notifications."""
        handler = getattr(self, '_NH_%s' % notification.name, Null)
        handler(notification)
        
    def _NH_SIPSessionNewIncoming(self, notification):
        """Handle incoming SIP session."""
        session = notification.sender
        
        # Get caller information
        caller_id = str(session.remote_identity.uri)
        if session.remote_identity.display_name:
            caller_id = f"{session.remote_identity.display_name} <{caller_id}>"
            
        log.info(f"Incoming call from: {caller_id}")
        
        # Auto-accept the call
        self.session = session
        
        # Start call in call agent service
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            self.active_call_id = loop.run_until_complete(
                self.call_agent.start_call(caller_id)
            )
            loop.close()
            log.info(f"Started AI call session: {self.active_call_id}")
        except Exception as e:
            log.error(f"Failed to start AI call session: {e}")
            
        # Accept the session with audio stream
        streams = [stream for stream in session.proposed_streams if stream.type == 'audio']
        if streams:
            session.accept(streams)
            log.info(f"Accepted call from {caller_id}")
        else:
            session.reject(415)  # Unsupported Media Type
            log.warning(f"Rejected call from {caller_id} - no audio stream")
            
    def _NH_SIPSessionDidStart(self, notification):
        """Handle session start."""
        session = notification.sender
        log.info(f"Session started with {session.remote_identity.uri}")
        
        # Get the audio stream
        for stream in session.streams:
            if stream.type == 'audio':
                self.audio_stream = stream
                break
                
        # Start audio processing thread
        if self.audio_stream and self.active_call_id:
            self.stop_processing.clear()
            self.processing_thread = Thread(target=self._audio_processing_loop)
            self.processing_thread.daemon = True
            self.processing_thread.start()
            
    def _NH_SIPSessionDidEnd(self, notification):
        """Handle session end."""
        session = notification.sender
        log.info(f"Session ended with {session.remote_identity.uri}")
        
        # Stop audio processing
        self.stop_processing.set()
        if self.processing_thread:
            self.processing_thread.join(timeout=5)
            
        # End call in call agent service
        if self.active_call_id:
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(
                    self.call_agent.end_call(self.active_call_id, "session_ended")
                )
                loop.close()
                log.info(f"Ended AI call session: {self.active_call_id}")
            except Exception as e:
                log.error(f"Failed to end AI call session: {e}")
                
        # Reset state
        self.session = None
        self.audio_stream = None
        self.active_call_id = None
        
    def _NH_SIPSessionDidFail(self, notification):
        """Handle session failure."""
        session = notification.sender
        log.warning(f"Session failed with {session.remote_identity.uri}: {notification.data.reason}")
        
        # Clean up
        if self.active_call_id:
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(
                    self.call_agent.end_call(self.active_call_id, "session_failed")
                )
                loop.close()
            except Exception as e:
                log.error(f"Failed to end AI call session: {e}")
                
        self.session = None
        self.audio_stream = None
        self.active_call_id = None
        
    def _NH_MediaStreamDidStart(self, notification):
        """Handle media stream start."""
        stream = notification.sender
        log.info(f"Media stream started: {stream.type}")
        
    def _NH_MediaStreamDidEnd(self, notification):
        """Handle media stream end."""
        stream = notification.sender
        log.info(f"Media stream ended: {stream.type}")
        
    def _NH_AudioStreamDidReceiveRTP(self, notification):
        """Handle incoming audio RTP packets."""
        # Add audio data to buffer for processing
        if self.active_call_id and not self.stop_processing.is_set():
            audio_data = notification.data.data
            self.audio_buffer.append(audio_data)
            
    def _audio_processing_loop(self):
        """Process audio data in a separate thread."""
        log.info("Started audio processing loop")
        
        accumulated_audio = bytearray()
        last_process_time = time.time()
        
        while not self.stop_processing.is_set():
            try:
                # Collect audio data from buffer
                while self.audio_buffer and not self.stop_processing.is_set():
                    audio_chunk = self.audio_buffer.popleft()
                    accumulated_audio.extend(audio_chunk)
                    
                # Process accumulated audio every 2 seconds or when we have enough data
                current_time = time.time()
                if (len(accumulated_audio) > 32000 or  # ~1 second at 16kHz
                    current_time - last_process_time > 2.0) and accumulated_audio:
                    
                    # Process audio through AI pipeline
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        
                        response_audio = loop.run_until_complete(
                            self.call_agent.process_audio(self.active_call_id, bytes(accumulated_audio))
                        )
                        
                        loop.close()
                        
                        # Play response audio through the stream
                        if response_audio and self.audio_stream:
                            self._play_audio_response(response_audio)
                            
                    except Exception as e:
                        log.error(f"Error processing audio: {e}")
                        
                    # Reset for next processing cycle
                    accumulated_audio.clear()
                    last_process_time = current_time
                    
                # Small delay to prevent busy waiting
                time.sleep(0.1)
                
            except Exception as e:
                log.error(f"Error in audio processing loop: {e}")
                time.sleep(1)
                
        log.info("Audio processing loop stopped")
        
    def _play_audio_response(self, audio_data):
        """Play AI response audio through the SIP stream."""
        try:
            if self.audio_stream and hasattr(self.audio_stream, 'send_audio'):
                # This is a simplified approach - in reality you'd need to handle
                # audio format conversion and proper RTP packet timing
                self.audio_stream.send_audio(audio_data)
                log.info(f"Played AI response audio ({len(audio_data)} bytes)")
            else:
                log.warning("Cannot play audio - no active audio stream")
        except Exception as e:
            log.error(f"Error playing audio response: {e}")


class AIIntegratedSIPApplication(SIPApplication):
    """SIP application with AI integration."""
    
    def __init__(self, call_agent_service):
        super().__init__()
        self.call_agent = call_agent_service
        self.ai_session = None
        
    def start(self, storage):
        """Start the SIP application."""
        super().start(storage)
        
        # Create AI session handler
        self.ai_session = AIAudioSession(self.call_agent)
        
        # Register for application notifications
        notification_center = NotificationCenter()
        notification_center.add_observer(self, name='SIPApplicationDidStart')
        
    def handle_notification(self, notification):
        """Handle application notifications."""
        handler = getattr(self, '_NH_%s' % notification.name, Null)
        handler(notification)
        
    def _NH_SIPApplicationDidStart(self, notification):
        """Handle application start."""
        log.info("AI-integrated SIP application started")
        
        # Get default account
        account_manager = AccountManager()
        default_account = account_manager.default_account
        
        if default_account:
            log.info(f"Using SIP account: {default_account.id}")
        else:
            log.error("No SIP account configured")


def main():
    """Main entry point for AI-integrated SIP client."""
    import signal
    
    # Setup logging
    log.level.current = log.level.INFO
    
    # Load configuration
    config = get_config()
    
    # Create call agent service
    call_agent = CallAgentService(config)
    
    # Start call agent in a separate thread
    def start_call_agent():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(call_agent.start())
        
    agent_thread = Thread(target=start_call_agent)
    agent_thread.daemon = True
    agent_thread.start()
    
    # Wait for call agent to start
    time.sleep(2)
    
    # Create and start SIP application
    config_directory = os.path.expanduser('~/.sipclient')
    storage = FileStorage(config_directory)
    
    application = AIIntegratedSIPApplication(call_agent)
    
    try:
        application.start(storage)
        log.info("AI SIP Client started. Waiting for calls...")
        log.info("Press Ctrl+C to stop")
        
        # Handle shutdown gracefully
        def signal_handler(signum, frame):
            log.info("Shutting down...")
            application.stop()
            sys.exit(0)
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Keep the application running
        reactor.run()
        
    except Exception as e:
        log.error(f"Failed to start AI SIP client: {e}")
        sys.exit(1)
    finally:
        # Stop call agent
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(call_agent.stop())
            loop.close()
        except:
            pass


if __name__ == '__main__':
    main()
