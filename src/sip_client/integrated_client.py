#!/usr/bin/env python3
"""
Properly integrated SIP client that auto-accepts calls and processes audio through AI.
This extends the working SIP client with real AI integration.
"""

import sys
import os
import asyncio
import threading
import time
import wave
import io
from pathlib import Path

# Import the working SIP client
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "ingridients"))

import importlib.util
spec = importlib.util.spec_from_file_location(
    "sip_audio_session3", 
    project_root / "ingridients" / "sip-audio-session3.py"
)
sip_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(sip_module)

# Import our AI components
sys.path.insert(0, str(project_root))
from src.call_agent.agent import CallAgentService
from src.call_agent.config import get_config


class AIIntegratedSIPClient(sip_module.SIPAudioApplication):
    """SIP client with proper AI integration."""
    
    def __init__(self):
        super().__init__()

        # Initialize AI components
        self.config = get_config()
        self.call_agent = None
        self.active_ai_calls = {}
        self.audio_buffers = {}
        self.ai_service_started = False

    def _safe_output(self, message):
        """Safely output a message."""
        if self.output:
            self.output.put(message)
        else:
            print(message.strip())
        
    def start(self, target, options):
        """Start the SIP client and AI service."""
        # Call parent start first to initialize output
        super().start(target, options)

        # Now start AI service
        if not self.ai_service_started:
            self._start_ai_service()
            self.ai_service_started = True

    def _start_ai_service(self):
        """Start the AI call agent service."""
        def start_agent():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            self.call_agent = CallAgentService(self.config)
            loop.run_until_complete(self.call_agent.start())

            # Keep the loop running
            try:
                loop.run_forever()
            except:
                pass

        agent_thread = threading.Thread(target=start_agent, daemon=True)
        agent_thread.start()

        # Wait for agent to start
        time.sleep(3)
        self._safe_output("🤖 AI Call Agent started\n")
        
    def _NH_SIPSessionNewIncoming(self, notification):
        """Handle incoming calls with AI integration."""
        session = notification.sender
        
        # Get caller information
        caller_id = str(session.remote_identity.uri)
        if session.remote_identity.display_name:
            caller_id = f"{session.remote_identity.display_name} <{caller_id}>"
            
        self._safe_output(f"🤖 Incoming call from: {caller_id}\n")
        
        # Start AI call session
        if self.call_agent:
            try:
                # Create new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                call_id = loop.run_until_complete(
                    self.call_agent.start_call(caller_id)
                )
                
                session_id = id(session)
                self.active_ai_calls[session_id] = call_id
                self.audio_buffers[session_id] = bytearray()
                
                self._safe_output(f"🤖 Started AI session: {call_id}\n")
                loop.close()

            except Exception as e:
                self._safe_output(f"🤖 AI session error: {e}\n")
        
        # AUTO-ACCEPT the call (this is the key fix!)
        streams = [stream for stream in session.proposed_streams if stream.type == 'audio']
        if streams:
            session.accept(streams)
            self._safe_output(f"🤖 Auto-accepted call from {caller_id}\n")
        else:
            session.reject(415)  # Unsupported Media Type
            
    def _NH_SIPSessionDidStart(self, notification):
        """Handle session start."""
        session = notification.sender
        session_id = id(session)
        
        self._safe_output(f"🤖 Call session started\n")
        
        # Call parent handler
        super()._NH_SIPSessionDidStart(notification)
        
        # Start audio processing for AI
        if session_id in self.active_ai_calls:
            self._start_audio_processing(session_id, session)
            
    def _NH_SIPSessionDidEnd(self, notification):
        """Handle session end."""
        session = notification.sender
        session_id = id(session)
        
        # End AI session
        if session_id in self.active_ai_calls:
            call_id = self.active_ai_calls[session_id]
            
            if self.call_agent:
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    loop.run_until_complete(
                        self.call_agent.end_call(call_id, "session_ended")
                    )
                    
                    loop.close()
                    self._safe_output(f"🤖 Ended AI session: {call_id}\n")

                except Exception as e:
                    self._safe_output(f"🤖 AI end error: {e}\n")
                    
            # Clean up
            del self.active_ai_calls[session_id]
            if session_id in self.audio_buffers:
                del self.audio_buffers[session_id]
                
        # Call parent handler
        super()._NH_SIPSessionDidEnd(notification)
        
    def _start_audio_processing(self, session_id, session):
        """Start processing audio for AI."""
        def audio_processor():
            self._safe_output(f"🤖 Started audio processing\n")
            
            while session_id in self.active_ai_calls:
                try:
                    # Check if we have enough audio to process
                    if (session_id in self.audio_buffers and 
                        len(self.audio_buffers[session_id]) > 32000):  # ~1 second at 16kHz
                        
                        # Get accumulated audio
                        audio_data = bytes(self.audio_buffers[session_id])
                        self.audio_buffers[session_id].clear()
                        
                        # Process through AI
                        call_id = self.active_ai_calls[session_id]
                        self._process_audio_with_ai(call_id, audio_data, session)
                        
                    time.sleep(0.5)  # Check every 500ms
                    
                except Exception as e:
                    self._safe_output(f"🤖 Audio processing error: {e}\n")
                    break
                    
        # Start audio processing thread
        thread = threading.Thread(target=audio_processor, daemon=True)
        thread.start()
        
    def _process_audio_with_ai(self, call_id, audio_data, session):
        """Process audio through AI pipeline."""
        try:
            if not self.call_agent:
                return
                
            # Convert raw audio to WAV format
            wav_data = self._convert_to_wav(audio_data)
            
            # Process through AI
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            response_audio = loop.run_until_complete(
                self.call_agent.process_audio(call_id, wav_data)
            )
            
            loop.close()
            
            if response_audio:
                self._safe_output(f"🤖 Generated AI response ({len(response_audio)} bytes)\n")
                # TODO: Play response audio back to caller
                # This would require integration with the SIP audio stream
            else:
                self._safe_output(f"🤖 No AI response (silence detected)\n")

        except Exception as e:
            self._safe_output(f"🤖 AI processing error: {e}\n")
            
    def _convert_to_wav(self, raw_audio):
        """Convert raw audio to WAV format."""
        try:
            # Create WAV file in memory
            wav_buffer = io.BytesIO()
            
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(16000)  # 16kHz
                wav_file.writeframes(raw_audio)
                
            return wav_buffer.getvalue()
            
        except Exception as e:
            self._safe_output(f"🤖 WAV conversion error: {e}\n")
            return raw_audio
            
    def _NH_AudioStreamDidReceiveRTP(self, notification):
        """Capture incoming audio for AI processing."""
        # Get the session
        stream = notification.sender
        session = stream.session
        session_id = id(session)
        
        # Add audio to buffer for AI processing
        if session_id in self.active_ai_calls:
            audio_data = notification.data.data
            if session_id in self.audio_buffers:
                self.audio_buffers[session_id].extend(audio_data)
                
        # Call parent handler for normal audio processing
        super()._NH_AudioStreamDidReceiveRTP(notification)


def main():
    """Main entry point."""
    print("🚀 AI-Integrated SIP Client")
    print("=" * 50)
    print("🤖 Features:")
    print("   ✅ Auto-accepts incoming calls")
    print("   ✅ Processes audio through AI pipeline")
    print("   ✅ Shows calls in web dashboard")
    print("   ✅ Real-time speech-to-text")
    print("   ✅ AI response generation")
    print("   ✅ Text-to-speech synthesis")
    print()
    print("🌐 Web dashboard: http://localhost:8000")
    print("📞 SIP client will register and handle calls automatically")
    print()

    try:
        # Create minimal options object
        class Options:
            def __init__(self):
                self.daemonize = False
                self.batch_mode = False
                self.enable_video = False
                self.log_register = False
                self.play_failure_code = False
                self.trace_sip = False
                self.trace_pjsip = False
                self.trace_notifications = False
                self.disable_sound = False
                self.enable_default_devices = False
                self.disable_ringtone = False
                self.disable_hanguptone = False
                self.enable_playback = False
                self.auto_record = False
                self.auto_reconnect = False
                self.config_directory = None
                self.account = None
                self.spool_dir = None
                self.playback_dir = None
                self.mute = False

        options = Options()

        # Create and start the integrated client
        client = AIIntegratedSIPClient()

        # Start the client (this will block)
        client.start(None, options)

    except KeyboardInterrupt:
        print("\n🛑 Shutting down AI SIP client...")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
