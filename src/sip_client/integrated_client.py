#!/usr/bin/env python3
"""
Properly integrated SIP client that auto-accepts calls and processes audio through AI.
This extends the working SIP client with real AI integration.
"""

import sys
import os
import asyncio
import threading
import time
import wave
import io
from pathlib import Path

# Import the working SIP client
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "ingridients"))

import importlib.util
spec = importlib.util.spec_from_file_location(
    "sip_audio_session3", 
    project_root / "ingridients" / "sip-audio-session3.py"
)
sip_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(sip_module)

# Import for HTTP requests
import urllib.request
import urllib.parse
import json


class AIAudioConsumer:
    """Custom audio consumer to capture audio data from SIP stream."""

    def __init__(self, session_id, sip_client):
        self.session_id = session_id
        self.sip_client = sip_client
        self.sample_rate = 16000
        self.channels = 1
        self.bytes_per_sample = 2
        self._is_active = False

    @property
    def is_active(self):
        """Required by IAudioPort interface."""
        return self._is_active

    @property
    def mixer(self):
        """Required by IAudioPort interface."""
        return self.sip_client.voice_audio_mixer

    @property
    def slot(self):
        """Required by IAudioPort interface."""
        return 0

    def start(self):
        """Start the audio consumer."""
        self._is_active = True
        self.sip_client._safe_output(f"🤖 Audio consumer started for session {self.session_id}\n")

    def stop(self):
        """Stop the audio consumer."""
        self._is_active = False
        self.sip_client._safe_output(f"🤖 Audio consumer stopped for session {self.session_id}\n")

    def write(self, data):
        """Called when audio data is available."""
        try:
            if not self._is_active:
                return

            # Add audio data to buffer for processing
            if self.session_id in self.sip_client.audio_buffers:
                self.sip_client.audio_buffers[self.session_id].extend(data)

                # Process audio when we have enough data (1 second worth)
                buffer_size = len(self.sip_client.audio_buffers[self.session_id])
                target_size = self.sample_rate * self.channels * self.bytes_per_sample  # 1 second

                if buffer_size >= target_size:
                    # Get call ID for this session
                    call_id = self.sip_client.active_ai_calls.get(self.session_id)
                    if call_id:
                        # Extract audio data
                        audio_data = bytes(self.sip_client.audio_buffers[self.session_id][:target_size])
                        self.sip_client.audio_buffers[self.session_id] = self.sip_client.audio_buffers[self.session_id][target_size:]

                        # Process in background thread
                        threading.Thread(
                            target=self.sip_client._process_audio_with_ai,
                            args=(call_id, audio_data, None),
                            daemon=True
                        ).start()

        except Exception as e:
            self.sip_client._safe_output(f"🤖 Audio consumer error: {e}\n")


class AIIntegratedSIPClient(sip_module.SIPAudioApplication):
    """SIP client with proper AI integration."""
    
    def __init__(self):
        super().__init__()

        # Initialize AI backend connection
        self.ai_backend_url = "http://localhost:8000"
        self.active_ai_calls = {}
        self.audio_buffers = {}
        self.backend_available = False

    def _safe_output(self, message):
        """Safely output a message."""
        if self.output:
            self.output.put(message)
        else:
            print(message.strip())
        
    def start(self, target, options):
        """Start the SIP client and check AI backend."""
        # Call parent start first to initialize output
        super().start(target, options)

        # Check AI backend availability
        self._check_ai_backend()

    def _check_ai_backend(self):
        """Check if AI backend is available."""
        try:
            req = urllib.request.Request(f"{self.ai_backend_url}/api/health")
            with urllib.request.urlopen(req, timeout=5) as response:
                if response.status == 200:
                    self.backend_available = True
                    self._safe_output("🤖 AI Backend connected successfully\n")
                else:
                    self._safe_output(f"🤖 AI Backend health check failed: {response.status}\n")
        except Exception as e:
            self._safe_output(f"🤖 AI Backend not available: {e}\n")
            self._safe_output("🤖 Make sure to run: python ai_backend.py\n")

    def _start_ai_call_http(self, caller_id):
        """Start AI call via HTTP API."""
        try:
            data = json.dumps({"caller_id": caller_id}).encode('utf-8')
            req = urllib.request.Request(
                f"{self.ai_backend_url}/api/calls/start",
                data=data,
                headers={'Content-Type': 'application/json'}
            )

            with urllib.request.urlopen(req, timeout=5) as response:
                if response.status == 200:
                    response_data = json.loads(response.read().decode('utf-8'))
                    return response_data.get("call_id")
                else:
                    self._safe_output(f"🤖 Backend error: {response.status}\n")
                    return None
        except Exception as e:
            self._safe_output(f"🤖 HTTP error: {e}\n")
            return None

    def _end_ai_call_http(self, call_id):
        """End AI call via HTTP API."""
        try:
            req = urllib.request.Request(
                f"{self.ai_backend_url}/api/calls/{call_id}/end",
                method='POST'
            )

            with urllib.request.urlopen(req, timeout=5) as response:
                return response.status == 200
        except Exception as e:
            self._safe_output(f"🤖 HTTP error ending call: {e}\n")
            return False
        
    def _NH_SIPSessionNewIncoming(self, notification):
        """Handle incoming calls with AI integration."""
        session = notification.sender
        
        # Get caller information
        caller_id = str(session.remote_identity.uri)
        if session.remote_identity.display_name:
            caller_id = f"{session.remote_identity.display_name} <{caller_id}>"
            
        self._safe_output(f"🤖 Incoming call from: {caller_id}\n")
        
        # Store session reference immediately
        session_id = id(session)
        if not hasattr(self, 'session_references'):
            self.session_references = {}
        self.session_references[session_id] = session
        self._safe_output(f"🤖 Stored session reference for ID: {session_id}\n")

        # Start AI call session via HTTP API
        if self.backend_available:
            try:
                call_id = self._start_ai_call_http(caller_id)
                if call_id:
                    self.active_ai_calls[session_id] = call_id
                    self.audio_buffers[session_id] = bytearray()
                    self._safe_output(f"🤖 Started AI session: {call_id}\n")
                else:
                    self._safe_output("🤖 Failed to start AI session\n")
            except Exception as e:
                self._safe_output(f"🤖 AI session error: {e}\n")
        
        # AUTO-ACCEPT the call (this is the key fix!)
        streams = [stream for stream in session.proposed_streams if stream.type == 'audio']
        if streams:
            session.accept(streams)
            self._safe_output(f"🤖 Auto-accepted call from {caller_id}\n")
        else:
            session.reject(415)  # Unsupported Media Type
            
    def _NH_SIPSessionDidStart(self, notification):
        """Handle session start."""
        session = notification.sender
        session_id = id(session)

        self._safe_output(f"🤖 Call session started\n")

        # Store session reference for audio playback
        if not hasattr(self, 'session_references'):
            self.session_references = {}
        self.session_references[session_id] = session

        # Call parent handler
        super()._NH_SIPSessionDidStart(notification)

        # Start audio processing for AI
        if session_id in self.active_ai_calls:
            self._start_audio_processing(session_id, session)

    def _NH_MediaStreamDidStart(self, notification):
        """Handle media stream start - this is where we can access audio."""
        stream = notification.sender
        session = stream.session
        session_id = id(session)

        self._safe_output(f"🤖 Media stream started: {stream.type}\n")

        # Call parent handler
        super()._NH_MediaStreamDidStart(notification)

        # Hook into audio stream if this is an audio stream
        if stream.type == 'audio' and session_id in self.active_ai_calls:
            self._hook_audio_stream(session_id, stream)

    def _hook_audio_stream(self, session_id, audio_stream):
        """Hook into the audio stream to capture audio data using built-in recording."""
        try:
            self._safe_output(f"🤖 Starting audio recording for session {session_id}\n")

            # Use the built-in audio recording functionality
            from sipsimple.configuration.settings import SIPSimpleSettings
            from datetime import datetime
            from application.system import makedirs

            settings = SIPSimpleSettings()
            session = audio_stream.session
            direction = session.direction
            remote = f"{session.remote_identity.uri.user}@{session.remote_identity.uri.host}"
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            filename = f"{timestamp}-{remote}-{direction}-ai.wav"

            # Create recording directory
            audio_dir = os.path.expanduser("~/.sipclient/audio")
            makedirs(audio_dir)
            recording_path = os.path.join(audio_dir, filename)

            # Start recording
            audio_stream.start_recording(recording_path)

            # Store recording info for processing
            if not hasattr(self, 'active_recordings'):
                self.active_recordings = {}
            self.active_recordings[session_id] = {
                'path': recording_path,
                'call_id': self.active_ai_calls.get(session_id),
                'last_processed_size': 0
            }

            self._safe_output(f"🤖 Recording started: {recording_path}\n")

            # Start real-time audio processing
            self._start_realtime_audio_processing(session_id, recording_path)

        except Exception as e:
            self._safe_output(f"🤖 Error starting recording: {e}\n")


            
    def _NH_SIPSessionDidEnd(self, notification):
        """Handle session end."""
        session = notification.sender
        session_id = id(session)
        
        # End AI session
        if session_id in self.active_ai_calls:
            call_id = self.active_ai_calls[session_id]

            if self.backend_available:
                try:
                    if self._end_ai_call_http(call_id):
                        self._safe_output(f"🤖 Ended AI session: {call_id}\n")
                    else:
                        self._safe_output(f"🤖 Failed to end AI session: {call_id}\n")
                except Exception as e:
                    self._safe_output(f"🤖 AI end error: {e}\n")
                    
            # Clean up
            del self.active_ai_calls[session_id]
            if session_id in self.audio_buffers:
                del self.audio_buffers[session_id]
                
        # Stop recording and process final audio
        if hasattr(self, 'active_recordings') and session_id in self.active_recordings:
            recording_info = self.active_recordings[session_id]
            call_id = recording_info['call_id']
            recording_path = recording_info['path']

            # Stop recording
            try:
                for stream in session.streams:
                    if stream.type == 'audio' and hasattr(stream, 'recorder') and stream.recorder:
                        stream.stop_recording()
                        self._safe_output(f"🤖 Stopped recording for session {session_id}\n")

                        # Process the final recorded audio
                        if call_id and os.path.exists(recording_path):
                            threading.Thread(
                                target=self._process_recorded_audio,
                                args=(call_id, recording_path),
                                daemon=True
                            ).start()
                        break
            except Exception as e:
                self._safe_output(f"🤖 Error stopping recording: {e}\n")

            del self.active_recordings[session_id]

        # Clean up any active audio players for this session
        self._cleanup_session_players(session_id)

        # Clean up session reference
        if hasattr(self, 'session_references') and session_id in self.session_references:
            del self.session_references[session_id]

        # Call parent handler
        super()._NH_SIPSessionDidEnd(notification)

    def _cleanup_session_players(self, session_id):
        """Clean up any active audio players for a session."""
        try:
            if hasattr(self, 'active_players'):
                players_to_remove = []
                for temp_path, player_info in self.active_players.items():
                    # Stop and remove all players (we could be more specific if needed)
                    try:
                        player_obj = player_info['player']
                        stream_obj = player_info['stream']

                        if hasattr(player_obj, 'stop'):
                            player_obj.stop()
                        if hasattr(stream_obj, 'bridge'):
                            stream_obj.bridge.remove(player_obj)

                        # Clean up temp file
                        try:
                            os.remove(temp_path)
                        except:
                            pass

                        players_to_remove.append(temp_path)

                    except Exception as e:
                        self._safe_output(f"🤖 Error cleaning up player: {e}\n")

                # Remove from active players
                for temp_path in players_to_remove:
                    if temp_path in self.active_players:
                        del self.active_players[temp_path]

                if players_to_remove:
                    self._safe_output(f"🤖 Cleaned up {len(players_to_remove)} audio players\n")

        except Exception as e:
            self._safe_output(f"🤖 Error in session player cleanup: {e}\n")
        
    def _start_audio_processing(self, session_id, session):
        """Start processing audio for AI - now handled by recording."""
        self._safe_output(f"🤖 Audio processing will be handled by real-time recording\n")

    def _start_realtime_audio_processing(self, session_id, recording_path):
        """Start real-time processing of the recording file as it grows."""
        def realtime_processor():
            self._safe_output(f"🤖 Starting real-time audio processing\n")

            call_id = self.active_recordings[session_id]['call_id']
            chunk_duration = 3.0  # Process 3-second chunks
            sample_rate = 16000  # Assuming 16kHz
            bytes_per_second = sample_rate * 2  # 16-bit audio = 2 bytes per sample
            chunk_size = int(chunk_duration * bytes_per_second)

            while session_id in self.active_ai_calls:
                try:
                    # Check if session is still active
                    if session_id not in self.active_recordings:
                        self._safe_output(f"🤖 Session {session_id} no longer active, stopping processing\n")
                        break

                    # Check if recording file exists and has grown
                    if os.path.exists(recording_path):
                        current_size = os.path.getsize(recording_path)
                        last_processed = self.active_recordings[session_id]['last_processed_size']

                        # If we have enough new audio data
                        if current_size > last_processed + chunk_size:
                            # Read the new chunk
                            with open(recording_path, 'rb') as f:
                                f.seek(last_processed)
                                chunk_data = f.read(chunk_size)

                            if len(chunk_data) >= chunk_size:
                                self._safe_output(f"🤖 Processing {len(chunk_data)} bytes of audio\n")

                                # Convert to proper WAV format
                                wav_data = self._convert_to_wav(chunk_data)

                                # Process this chunk through AI (with error handling)
                                response_audio = self._process_audio_http(call_id, wav_data)

                                if response_audio:
                                    self._safe_output(f"🤖 Generated real-time AI response ({len(response_audio)} bytes)\n")

                                    # Play response back to caller
                                    self._play_response_audio(session_id, response_audio)

                                    # Check if we can see the transcript in the dashboard
                                    self._check_call_status(call_id)
                                else:
                                    self._safe_output(f"🤖 No response for this audio chunk (silence detected)\n")

                                # Update processed position
                                self.active_recordings[session_id]['last_processed_size'] = last_processed + chunk_size

                    # Check every 1 second for new audio
                    time.sleep(1.0)

                except Exception as e:
                    self._safe_output(f"🤖 Real-time processing error: {e}\n")
                    break

            self._safe_output(f"🤖 Real-time audio processing ended\n")

        # Start processing thread
        processor_thread = threading.Thread(target=realtime_processor, daemon=True)
        processor_thread.start()

    def _process_recorded_audio(self, call_id, recording_path):
        """Process the recorded audio file through AI pipeline."""
        try:
            self._safe_output(f"🤖 Processing recorded audio: {recording_path}\n")

            # Wait a moment for file to be fully written
            time.sleep(1)

            # Read the audio file
            if os.path.exists(recording_path):
                with open(recording_path, 'rb') as f:
                    audio_data = f.read()

                # Process through AI
                response_audio = self._process_audio_http(call_id, audio_data)

                if response_audio:
                    self._safe_output(f"🤖 Generated AI response from recording ({len(response_audio)} bytes)\n")
                else:
                    self._safe_output(f"🤖 No AI response from recording\n")

                # Clean up recording file
                try:
                    os.remove(recording_path)
                    self._safe_output(f"🤖 Cleaned up recording file\n")
                except:
                    pass
            else:
                self._safe_output(f"🤖 Recording file not found: {recording_path}\n")

        except Exception as e:
            self._safe_output(f"🤖 Error processing recorded audio: {e}\n")
        
    def _process_audio_with_ai(self, call_id, audio_data, session):
        """Process audio through AI pipeline via HTTP API."""
        try:
            if not self.backend_available:
                return

            # Convert raw audio to WAV format
            wav_data = self._convert_to_wav(audio_data)

            # Send to AI backend via HTTP
            response_audio = self._process_audio_http(call_id, wav_data)

            if response_audio:
                self._safe_output(f"🤖 Generated AI response ({len(response_audio)} bytes)\n")
                # TODO: Play response audio back to caller
                # This would require integration with the SIP audio stream
            else:
                self._safe_output(f"🤖 No AI response (silence detected)\n")

        except Exception as e:
            self._safe_output(f"🤖 AI processing error: {e}\n")

    def _process_audio_http(self, call_id, wav_data):
        """Process audio via HTTP API."""
        try:
            # Create multipart form data
            boundary = '----WebKitFormBoundary7MA4YWxkTrZu0gW'
            body = (
                f'--{boundary}\r\n'
                f'Content-Disposition: form-data; name="audio"; filename="audio.wav"\r\n'
                f'Content-Type: audio/wav\r\n\r\n'
            ).encode('utf-8') + wav_data + f'\r\n--{boundary}--\r\n'.encode('utf-8')

            req = urllib.request.Request(
                f"{self.ai_backend_url}/api/calls/{call_id}/process_audio",
                data=body,
                headers={
                    'Content-Type': f'multipart/form-data; boundary={boundary}',
                    'Content-Length': str(len(body))
                }
            )

            with urllib.request.urlopen(req, timeout=10) as response:
                if response.status in [200, 204]:
                    return response.read()  # Response audio (may be empty for 204)
                else:
                    self._safe_output(f"🤖 Audio processing error: {response.status}\n")
                    return None

        except Exception as e:
            error_msg = str(e)
            if "404" in error_msg:
                self._safe_output(f"🤖 Call ended (404 - call not found)\n")
            else:
                self._safe_output(f"🤖 Audio HTTP error: {e}\n")
            return None

    def _check_call_status(self, call_id):
        """Check call status to see transcript updates."""
        try:
            req = urllib.request.Request(f"{self.ai_backend_url}/api/calls/{call_id}")
            with urllib.request.urlopen(req, timeout=5) as response:
                if response.status == 200:
                    call_data = json.loads(response.read().decode('utf-8'))
                    # Check different possible transcript fields
                    transcript = call_data.get('transcript', '')
                    conversation = call_data.get('conversation', {})
                    turns = conversation.get('turns', []) if conversation else []

                    if transcript:
                        self._safe_output(f"🤖 Current transcript: {transcript[:100]}...\n")
                    elif turns:
                        latest_turn = turns[-1] if turns else {}
                        speaker = latest_turn.get('speaker', 'unknown')
                        text = latest_turn.get('text', '')
                        self._safe_output(f"🤖 Latest turn ({speaker}): {text[:100]}...\n")
                    else:
                        self._safe_output(f"🤖 No transcript yet in call {call_id}\n")
        except Exception as e:
            self._safe_output(f"🤖 Error checking call status: {e}\n")

    def _play_response_audio(self, session_id, audio_data):
        """Play AI response audio back to the caller via SIP stream."""
        try:
            self._safe_output(f"🤖 Playing AI response to caller ({len(audio_data)} bytes)\n")

            # Find the active session and audio stream
            active_session = self._find_session_by_id(session_id)
            if not active_session:
                self._safe_output(f"🤖 Session {session_id} not found for audio playback\n")
                return

            self._safe_output(f"🤖 Found session for playback: {active_session}\n")

            # Find the audio stream
            audio_stream = None
            self._safe_output(f"🤖 Session has {len(active_session.streams)} streams\n")

            for stream in active_session.streams:
                self._safe_output(f"🤖 Stream type: {stream.type}, has bridge: {hasattr(stream, 'bridge')}\n")
                if stream.type == 'audio':
                    audio_stream = stream
                    break

            if not audio_stream:
                self._safe_output(f"🤖 No audio stream found for playback\n")
                return

            self._safe_output(f"🤖 Found audio stream: {audio_stream}\n")

            # Create and play audio through SIP bridge
            self._play_audio_through_bridge(audio_stream, audio_data)

        except Exception as e:
            self._safe_output(f"🤖 Error playing response audio: {e}\n")

    def _find_session_by_id(self, session_id):
        """Find the SIP session by session ID."""
        try:
            # Store session references when they're created
            if not hasattr(self, 'session_references'):
                self.session_references = {}

            self._safe_output(f"🤖 Looking for session ID: {session_id}\n")
            self._safe_output(f"🤖 Available session IDs: {list(self.session_references.keys())}\n")

            # Return stored session reference
            session = self.session_references.get(session_id)
            if session:
                self._safe_output(f"🤖 Found session: {session}\n")
            else:
                self._safe_output(f"🤖 Session {session_id} not in references\n")
            return session

        except Exception as e:
            self._safe_output(f"🤖 Error finding session: {e}\n")
            return None

    def _play_audio_through_bridge(self, audio_stream, audio_data):
        """Play audio data through the SIP audio bridge."""
        try:
            self._safe_output(f"🤖 Attempting to play audio through bridge\n")
            self._safe_output(f"🤖 Audio stream has bridge: {hasattr(audio_stream, 'bridge')}\n")

            if not hasattr(audio_stream, 'bridge'):
                self._safe_output(f"🤖 Audio stream has no bridge attribute\n")
                return

            from sipsimple.audio import AudioPlayer
            import tempfile

            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name

            self._safe_output(f"🤖 Created temp audio file: {temp_path} ({len(audio_data)} bytes)\n")

            # Create audio player
            self._safe_output(f"🤖 Creating AudioPlayer with mixer: {self.voice_audio_mixer}\n")

            player = AudioPlayer(
                mixer=self.voice_audio_mixer,
                filename=temp_path
            )

            self._safe_output(f"🤖 Created AudioPlayer: {player}\n")

            # Add player to the audio bridge
            self._safe_output(f"🤖 Adding player to bridge: {audio_stream.bridge}\n")
            audio_stream.bridge.add(player)

            # Start playing
            self._safe_output(f"🤖 Starting player...\n")
            player.start()

            self._safe_output(f"🤖 Started playing AI response through SIP bridge\n")

            # Store player reference for cleanup
            if not hasattr(self, 'active_players'):
                self.active_players = {}
            self.active_players[temp_path] = {
                'player': player,
                'stream': audio_stream,
                'temp_path': temp_path
            }

            # Schedule cleanup after playback
            def cleanup_player():
                try:
                    import time
                    time.sleep(10)  # Wait for playback to finish

                    if temp_path in self.active_players:
                        player_info = self.active_players[temp_path]
                        player_obj = player_info['player']
                        stream_obj = player_info['stream']

                        # Stop and remove player
                        if hasattr(player_obj, 'stop'):
                            player_obj.stop()
                        if hasattr(stream_obj, 'bridge'):
                            stream_obj.bridge.remove(player_obj)

                        # Clean up temp file
                        try:
                            os.remove(temp_path)
                            self._safe_output(f"🤖 Cleaned up audio player and temp file\n")
                        except:
                            pass

                        del self.active_players[temp_path]

                except Exception as e:
                    self._safe_output(f"🤖 Error in player cleanup: {e}\n")

            # Start cleanup thread
            cleanup_thread = threading.Thread(target=cleanup_player, daemon=True)
            cleanup_thread.start()

        except Exception as e:
            self._safe_output(f"🤖 Error playing audio through bridge: {e}\n")
            
    def _convert_to_wav(self, raw_audio):
        """Convert raw audio to WAV format."""
        try:
            # Create WAV file in memory
            wav_buffer = io.BytesIO()
            
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(16000)  # 16kHz
                wav_file.writeframes(raw_audio)
                
            return wav_buffer.getvalue()
            
        except Exception as e:
            self._safe_output(f"🤖 WAV conversion error: {e}\n")
            return raw_audio
            
    def _NH_AudioStreamDidReceiveRTP(self, notification):
        """Capture incoming audio for AI processing."""
        # Get the session
        stream = notification.sender
        session = stream.session
        session_id = id(session)
        
        # Add audio to buffer for AI processing
        if session_id in self.active_ai_calls:
            audio_data = notification.data.data
            if session_id in self.audio_buffers:
                self.audio_buffers[session_id].extend(audio_data)
                
        # Call parent handler for normal audio processing
        super()._NH_AudioStreamDidReceiveRTP(notification)


def main():
    """Main entry point."""
    print("🚀 AI-Integrated SIP Client")
    print("=" * 50)
    print("🤖 Features:")
    print("   ✅ Auto-accepts incoming calls")
    print("   ✅ Processes audio through AI pipeline")
    print("   ✅ Shows calls in web dashboard")
    print("   ✅ Real-time speech-to-text")
    print("   ✅ AI response generation")
    print("   ✅ Text-to-speech synthesis")
    print()
    print("🌐 Web dashboard: http://localhost:8000")
    print("📞 SIP client will register and handle calls automatically")
    print()

    try:
        # Create minimal options object
        class Options:
            def __init__(self):
                self.daemonize = False
                self.batch_mode = False
                self.enable_video = False
                self.log_register = False
                self.play_failure_code = False
                self.trace_sip = False
                self.trace_pjsip = False
                self.trace_notifications = False
                self.disable_sound = False
                self.enable_default_devices = False
                self.disable_ringtone = False
                self.disable_hanguptone = False
                self.enable_playback = False
                self.auto_record = True  # Enable auto-recording for AI processing
                self.auto_reconnect = False
                self.config_directory = None
                self.account = None
                self.spool_dir = None
                self.playback_dir = None
                self.mute = False

        options = Options()

        # Create and start the integrated client
        client = AIIntegratedSIPClient()

        # Start the client (this will block)
        client.start(None, options)

    except KeyboardInterrupt:
        print("\n🛑 Shutting down AI SIP client...")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
