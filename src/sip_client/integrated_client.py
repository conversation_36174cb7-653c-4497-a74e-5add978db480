#!/usr/bin/env python3
"""
Properly integrated SIP client that auto-accepts calls and processes audio through AI.
This extends the working SIP client with real AI integration.
"""

import sys
import os
import asyncio
import threading
import time
import wave
import io
from pathlib import Path

# Import the working SIP client
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "ingridients"))

import importlib.util
spec = importlib.util.spec_from_file_location(
    "sip_audio_session3", 
    project_root / "ingridients" / "sip-audio-session3.py"
)
sip_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(sip_module)

# Import for HTTP requests
import urllib.request
import urllib.parse
import json


class AIAudioConsumer:
    """Custom audio consumer to capture audio data from SIP stream."""

    def __init__(self, session_id, sip_client):
        self.session_id = session_id
        self.sip_client = sip_client
        self.sample_rate = 16000
        self.channels = 1
        self.bytes_per_sample = 2

    def start(self):
        """Start the audio consumer."""
        self.sip_client._safe_output(f"🤖 Audio consumer started for session {self.session_id}\n")

    def stop(self):
        """Stop the audio consumer."""
        self.sip_client._safe_output(f"🤖 Audio consumer stopped for session {self.session_id}\n")

    def write(self, data):
        """Called when audio data is available."""
        try:
            # Add audio data to buffer for processing
            if self.session_id in self.sip_client.audio_buffers:
                self.sip_client.audio_buffers[self.session_id].extend(data)

                # Process audio when we have enough data (1 second worth)
                buffer_size = len(self.sip_client.audio_buffers[self.session_id])
                target_size = self.sample_rate * self.channels * self.bytes_per_sample  # 1 second

                if buffer_size >= target_size:
                    # Get call ID for this session
                    call_id = self.sip_client.active_ai_calls.get(self.session_id)
                    if call_id:
                        # Extract audio data
                        audio_data = bytes(self.sip_client.audio_buffers[self.session_id][:target_size])
                        self.sip_client.audio_buffers[self.session_id] = self.sip_client.audio_buffers[self.session_id][target_size:]

                        # Process in background thread
                        threading.Thread(
                            target=self.sip_client._process_audio_with_ai,
                            args=(call_id, audio_data, None),
                            daemon=True
                        ).start()

        except Exception as e:
            self.sip_client._safe_output(f"🤖 Audio consumer error: {e}\n")


class AIIntegratedSIPClient(sip_module.SIPAudioApplication):
    """SIP client with proper AI integration."""
    
    def __init__(self):
        super().__init__()

        # Initialize AI backend connection
        self.ai_backend_url = "http://localhost:8000"
        self.active_ai_calls = {}
        self.audio_buffers = {}
        self.backend_available = False

    def _safe_output(self, message):
        """Safely output a message."""
        if self.output:
            self.output.put(message)
        else:
            print(message.strip())
        
    def start(self, target, options):
        """Start the SIP client and check AI backend."""
        # Call parent start first to initialize output
        super().start(target, options)

        # Check AI backend availability
        self._check_ai_backend()

    def _check_ai_backend(self):
        """Check if AI backend is available."""
        try:
            req = urllib.request.Request(f"{self.ai_backend_url}/api/health")
            with urllib.request.urlopen(req, timeout=5) as response:
                if response.status == 200:
                    self.backend_available = True
                    self._safe_output("🤖 AI Backend connected successfully\n")
                else:
                    self._safe_output(f"🤖 AI Backend health check failed: {response.status}\n")
        except Exception as e:
            self._safe_output(f"🤖 AI Backend not available: {e}\n")
            self._safe_output("🤖 Make sure to run: python ai_backend.py\n")

    def _start_ai_call_http(self, caller_id):
        """Start AI call via HTTP API."""
        try:
            data = json.dumps({"caller_id": caller_id}).encode('utf-8')
            req = urllib.request.Request(
                f"{self.ai_backend_url}/api/calls/start",
                data=data,
                headers={'Content-Type': 'application/json'}
            )

            with urllib.request.urlopen(req, timeout=5) as response:
                if response.status == 200:
                    response_data = json.loads(response.read().decode('utf-8'))
                    return response_data.get("call_id")
                else:
                    self._safe_output(f"🤖 Backend error: {response.status}\n")
                    return None
        except Exception as e:
            self._safe_output(f"🤖 HTTP error: {e}\n")
            return None

    def _end_ai_call_http(self, call_id):
        """End AI call via HTTP API."""
        try:
            req = urllib.request.Request(
                f"{self.ai_backend_url}/api/calls/{call_id}/end",
                method='POST'
            )

            with urllib.request.urlopen(req, timeout=5) as response:
                return response.status == 200
        except Exception as e:
            self._safe_output(f"🤖 HTTP error ending call: {e}\n")
            return False
        
    def _NH_SIPSessionNewIncoming(self, notification):
        """Handle incoming calls with AI integration."""
        session = notification.sender
        
        # Get caller information
        caller_id = str(session.remote_identity.uri)
        if session.remote_identity.display_name:
            caller_id = f"{session.remote_identity.display_name} <{caller_id}>"
            
        self._safe_output(f"🤖 Incoming call from: {caller_id}\n")
        
        # Start AI call session via HTTP API
        if self.backend_available:
            try:
                call_id = self._start_ai_call_http(caller_id)
                if call_id:
                    session_id = id(session)
                    self.active_ai_calls[session_id] = call_id
                    self.audio_buffers[session_id] = bytearray()
                    self._safe_output(f"🤖 Started AI session: {call_id}\n")
                else:
                    self._safe_output("🤖 Failed to start AI session\n")
            except Exception as e:
                self._safe_output(f"🤖 AI session error: {e}\n")
        
        # AUTO-ACCEPT the call (this is the key fix!)
        streams = [stream for stream in session.proposed_streams if stream.type == 'audio']
        if streams:
            session.accept(streams)
            self._safe_output(f"🤖 Auto-accepted call from {caller_id}\n")
        else:
            session.reject(415)  # Unsupported Media Type
            
    def _NH_SIPSessionDidStart(self, notification):
        """Handle session start."""
        session = notification.sender
        session_id = id(session)

        self._safe_output(f"🤖 Call session started\n")

        # Call parent handler
        super()._NH_SIPSessionDidStart(notification)

        # Start audio processing for AI
        if session_id in self.active_ai_calls:
            self._start_audio_processing(session_id, session)

    def _NH_MediaStreamDidStart(self, notification):
        """Handle media stream start - this is where we can access audio."""
        stream = notification.sender
        session = stream.session
        session_id = id(session)

        self._safe_output(f"🤖 Media stream started: {stream.type}\n")

        # Call parent handler
        super()._NH_MediaStreamDidStart(notification)

        # Hook into audio stream if this is an audio stream
        if stream.type == 'audio' and session_id in self.active_ai_calls:
            self._hook_audio_stream(session_id, stream)

    def _hook_audio_stream(self, session_id, audio_stream):
        """Hook into the audio stream to capture audio data."""
        try:
            self._safe_output(f"🤖 Hooking into audio stream for session {session_id}\n")

            # Create a custom audio consumer to capture audio
            audio_consumer = AIAudioConsumer(session_id, self)

            # Store consumer reference
            if not hasattr(self, 'audio_consumers'):
                self.audio_consumers = {}
            self.audio_consumers[session_id] = audio_consumer

            # Add consumer to the audio bridge
            audio_stream.bridge.add(audio_consumer)

            self._safe_output(f"🤖 Audio consumer added to bridge for session {session_id}\n")

        except Exception as e:
            self._safe_output(f"🤖 Error hooking audio stream: {e}\n")


            
    def _NH_SIPSessionDidEnd(self, notification):
        """Handle session end."""
        session = notification.sender
        session_id = id(session)
        
        # End AI session
        if session_id in self.active_ai_calls:
            call_id = self.active_ai_calls[session_id]

            if self.backend_available:
                try:
                    if self._end_ai_call_http(call_id):
                        self._safe_output(f"🤖 Ended AI session: {call_id}\n")
                    else:
                        self._safe_output(f"🤖 Failed to end AI session: {call_id}\n")
                except Exception as e:
                    self._safe_output(f"🤖 AI end error: {e}\n")
                    
            # Clean up
            del self.active_ai_calls[session_id]
            if session_id in self.audio_buffers:
                del self.audio_buffers[session_id]
                
        # Call parent handler
        super()._NH_SIPSessionDidEnd(notification)
        
    def _start_audio_processing(self, session_id, session):
        """Start processing audio for AI - now handled by audio consumer."""
        self._safe_output(f"🤖 Audio processing will be handled by audio consumer\n")
        
    def _process_audio_with_ai(self, call_id, audio_data, session):
        """Process audio through AI pipeline via HTTP API."""
        try:
            if not self.backend_available:
                return

            # Convert raw audio to WAV format
            wav_data = self._convert_to_wav(audio_data)

            # Send to AI backend via HTTP
            response_audio = self._process_audio_http(call_id, wav_data)

            if response_audio:
                self._safe_output(f"🤖 Generated AI response ({len(response_audio)} bytes)\n")
                # TODO: Play response audio back to caller
                # This would require integration with the SIP audio stream
            else:
                self._safe_output(f"🤖 No AI response (silence detected)\n")

        except Exception as e:
            self._safe_output(f"🤖 AI processing error: {e}\n")

    def _process_audio_http(self, call_id, wav_data):
        """Process audio via HTTP API."""
        try:
            # Create multipart form data
            boundary = '----WebKitFormBoundary7MA4YWxkTrZu0gW'
            body = (
                f'--{boundary}\r\n'
                f'Content-Disposition: form-data; name="audio"; filename="audio.wav"\r\n'
                f'Content-Type: audio/wav\r\n\r\n'
            ).encode('utf-8') + wav_data + f'\r\n--{boundary}--\r\n'.encode('utf-8')

            req = urllib.request.Request(
                f"{self.ai_backend_url}/api/calls/{call_id}/process_audio",
                data=body,
                headers={
                    'Content-Type': f'multipart/form-data; boundary={boundary}',
                    'Content-Length': str(len(body))
                }
            )

            with urllib.request.urlopen(req, timeout=10) as response:
                if response.status in [200, 204]:
                    return response.read()  # Response audio (may be empty for 204)
                else:
                    self._safe_output(f"🤖 Audio processing error: {response.status}\n")
                    return None

        except Exception as e:
            self._safe_output(f"🤖 Audio HTTP error: {e}\n")
            return None
            
    def _convert_to_wav(self, raw_audio):
        """Convert raw audio to WAV format."""
        try:
            # Create WAV file in memory
            wav_buffer = io.BytesIO()
            
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(16000)  # 16kHz
                wav_file.writeframes(raw_audio)
                
            return wav_buffer.getvalue()
            
        except Exception as e:
            self._safe_output(f"🤖 WAV conversion error: {e}\n")
            return raw_audio
            
    def _NH_AudioStreamDidReceiveRTP(self, notification):
        """Capture incoming audio for AI processing."""
        # Get the session
        stream = notification.sender
        session = stream.session
        session_id = id(session)
        
        # Add audio to buffer for AI processing
        if session_id in self.active_ai_calls:
            audio_data = notification.data.data
            if session_id in self.audio_buffers:
                self.audio_buffers[session_id].extend(audio_data)
                
        # Call parent handler for normal audio processing
        super()._NH_AudioStreamDidReceiveRTP(notification)


def main():
    """Main entry point."""
    print("🚀 AI-Integrated SIP Client")
    print("=" * 50)
    print("🤖 Features:")
    print("   ✅ Auto-accepts incoming calls")
    print("   ✅ Processes audio through AI pipeline")
    print("   ✅ Shows calls in web dashboard")
    print("   ✅ Real-time speech-to-text")
    print("   ✅ AI response generation")
    print("   ✅ Text-to-speech synthesis")
    print()
    print("🌐 Web dashboard: http://localhost:8000")
    print("📞 SIP client will register and handle calls automatically")
    print()

    try:
        # Create minimal options object
        class Options:
            def __init__(self):
                self.daemonize = False
                self.batch_mode = False
                self.enable_video = False
                self.log_register = False
                self.play_failure_code = False
                self.trace_sip = False
                self.trace_pjsip = False
                self.trace_notifications = False
                self.disable_sound = False
                self.enable_default_devices = False
                self.disable_ringtone = False
                self.disable_hanguptone = False
                self.enable_playback = False
                self.auto_record = False
                self.auto_reconnect = False
                self.config_directory = None
                self.account = None
                self.spool_dir = None
                self.playback_dir = None
                self.mute = False

        options = Options()

        # Create and start the integrated client
        client = AIIntegratedSIPClient()

        # Start the client (this will block)
        client.start(None, options)

    except KeyboardInterrupt:
        print("\n🛑 Shutting down AI SIP client...")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
