"""Real-time audio processing pipeline for the AI SIP Call Agent."""

import asyncio
import logging
import time
import wave
import io
from typing import Optional, Callable, List
from dataclasses import dataclass
from collections import deque
import numpy as np

from .processor import AudioProcessor, VoiceActivityDetector
from ..call_agent.config import Config, get_config

logger = logging.getLogger(__name__)


@dataclass
class AudioChunk:
    """Represents a chunk of audio data with metadata."""
    data: bytes
    timestamp: float
    sample_rate: int
    channels: int
    chunk_id: int


class AudioBuffer:
    """Circular buffer for audio chunks with voice activity detection."""
    
    def __init__(self, max_duration: float = 30.0, sample_rate: int = 16000):
        self.max_duration = max_duration
        self.sample_rate = sample_rate
        self.max_chunks = int(max_duration * sample_rate / 1024)  # Assuming 1024 samples per chunk
        
        self.chunks: deque[AudioChunk] = deque(maxlen=self.max_chunks)
        self.total_duration = 0.0
        self.chunk_counter = 0
        
    def add_chunk(self, audio_data: bytes, timestamp: float, 
                  sample_rate: int = 16000, channels: int = 1) -> AudioChunk:
        """Add an audio chunk to the buffer."""
        chunk = AudioChunk(
            data=audio_data,
            timestamp=timestamp,
            sample_rate=sample_rate,
            channels=channels,
            chunk_id=self.chunk_counter
        )
        
        self.chunks.append(chunk)
        self.chunk_counter += 1
        
        # Update total duration
        chunk_duration = len(audio_data) / (sample_rate * channels * 2)  # 16-bit samples
        self.total_duration = min(self.total_duration + chunk_duration, self.max_duration)
        
        return chunk
        
    def get_audio_data(self, start_time: Optional[float] = None, 
                      end_time: Optional[float] = None) -> bytes:
        """Get concatenated audio data from the buffer."""
        if not self.chunks:
            return b''
            
        # If no time range specified, return all chunks
        if start_time is None and end_time is None:
            return b''.join(chunk.data for chunk in self.chunks)
            
        # Filter chunks by time range
        filtered_chunks = []
        for chunk in self.chunks:
            if start_time is not None and chunk.timestamp < start_time:
                continue
            if end_time is not None and chunk.timestamp > end_time:
                break
            filtered_chunks.append(chunk)
            
        return b''.join(chunk.data for chunk in filtered_chunks)
        
    def clear(self):
        """Clear the buffer."""
        self.chunks.clear()
        self.total_duration = 0.0
        
    def get_duration(self) -> float:
        """Get total duration of audio in buffer."""
        return self.total_duration


class RealTimeAudioPipeline:
    """Real-time audio processing pipeline with VAD and buffering."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.audio_processor = AudioProcessor(self.config)
        self.vad = VoiceActivityDetector(self.config)
        
        # Audio buffering
        self.audio_buffer = AudioBuffer(
            max_duration=self.config.audio.max_recording_duration,
            sample_rate=self.config.audio.sample_rate
        )
        
        # Processing state
        self.is_processing = False
        self.speech_start_time = None
        self.last_chunk_time = time.time()
        
        # Callbacks
        self.on_speech_detected: Optional[Callable[[bytes], None]] = None
        self.on_silence_detected: Optional[Callable[[], None]] = None
        self.on_audio_ready: Optional[Callable[[bytes], None]] = None
        
    async def process_chunk(self, audio_data: bytes) -> Optional[bytes]:
        """Process an incoming audio chunk."""
        current_time = time.time()
        
        # Add chunk to buffer
        chunk = self.audio_buffer.add_chunk(audio_data, current_time)
        
        # Voice activity detection
        is_speech, should_process = self.vad.process_chunk(audio_data, current_time)
        
        # Handle speech detection
        if is_speech and self.speech_start_time is None:
            self.speech_start_time = current_time
            if self.on_speech_detected:
                await self._safe_callback(self.on_speech_detected, audio_data)
                
        # Handle silence detection
        elif not is_speech and self.speech_start_time is not None:
            if self.on_silence_detected:
                await self._safe_callback(self.on_silence_detected)
                
        # Process accumulated audio if needed
        if should_process and not self.is_processing:
            await self._process_accumulated_audio()
            
        self.last_chunk_time = current_time
        return None
        
    async def _process_accumulated_audio(self) -> Optional[bytes]:
        """Process accumulated audio in the buffer."""
        if self.is_processing:
            return None
            
        self.is_processing = True
        
        try:
            # Get audio data from buffer
            audio_data = self.audio_buffer.get_audio_data()
            
            if len(audio_data) == 0:
                return None
                
            # Process audio for STT
            processed_audio = self.audio_processor.process_for_stt(audio_data)
            
            # Clear buffer for next speech segment
            self.audio_buffer.clear()
            self.speech_start_time = None
            self.vad.reset()
            
            # Notify that audio is ready for processing
            if self.on_audio_ready:
                await self._safe_callback(self.on_audio_ready, processed_audio)
                
            return processed_audio
            
        except Exception as e:
            logger.error(f"Error processing accumulated audio: {e}")
            return None
        finally:
            self.is_processing = False
            
    async def force_process(self) -> Optional[bytes]:
        """Force processing of current buffer contents."""
        return await self._process_accumulated_audio()
        
    async def _safe_callback(self, callback: Callable, *args):
        """Safely execute a callback."""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(*args)
            else:
                callback(*args)
        except Exception as e:
            logger.error(f"Error in callback: {e}")
            
    def reset(self):
        """Reset the pipeline state."""
        self.audio_buffer.clear()
        self.vad.reset()
        self.speech_start_time = None
        self.is_processing = False


class AudioStreamProcessor:
    """Processes continuous audio streams with chunking and buffering."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.pipeline = RealTimeAudioPipeline(self.config)
        
        # Stream state
        self.is_active = False
        self.chunk_size = self.config.audio.chunk_size
        self.sample_rate = self.config.audio.sample_rate
        
        # Processing queue
        self.processing_queue = asyncio.Queue()
        self.processor_task = None
        
    async def start(self):
        """Start the audio stream processor."""
        if self.is_active:
            return
            
        self.is_active = True
        self.processor_task = asyncio.create_task(self._process_queue())
        logger.info("Audio stream processor started")
        
    async def stop(self):
        """Stop the audio stream processor."""
        if not self.is_active:
            return
            
        self.is_active = False
        
        if self.processor_task:
            self.processor_task.cancel()
            try:
                await self.processor_task
            except asyncio.CancelledError:
                pass
                
        logger.info("Audio stream processor stopped")
        
    async def process_stream_chunk(self, audio_data: bytes) -> None:
        """Add an audio chunk to the processing queue."""
        if self.is_active:
            await self.processing_queue.put(audio_data)
            
    async def _process_queue(self):
        """Process audio chunks from the queue."""
        while self.is_active:
            try:
                # Get chunk from queue with timeout
                audio_data = await asyncio.wait_for(
                    self.processing_queue.get(), 
                    timeout=1.0
                )
                
                # Process the chunk
                await self.pipeline.process_chunk(audio_data)
                
            except asyncio.TimeoutError:
                # Check for timeout in speech detection
                current_time = time.time()
                if (self.pipeline.speech_start_time and 
                    current_time - self.pipeline.last_chunk_time > 2.0):
                    # Force process if we haven't received audio for a while
                    await self.pipeline.force_process()
                    
            except Exception as e:
                logger.error(f"Error processing audio queue: {e}")
                
    def set_callbacks(self, 
                     on_speech_detected: Optional[Callable] = None,
                     on_silence_detected: Optional[Callable] = None,
                     on_audio_ready: Optional[Callable] = None):
        """Set callback functions for audio events."""
        if on_speech_detected:
            self.pipeline.on_speech_detected = on_speech_detected
        if on_silence_detected:
            self.pipeline.on_silence_detected = on_silence_detected
        if on_audio_ready:
            self.pipeline.on_audio_ready = on_audio_ready


class AudioFileProcessor:
    """Processes audio files for batch processing."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.audio_processor = AudioProcessor(self.config)
        
    async def process_file(self, file_path: str) -> Optional[bytes]:
        """Process an audio file."""
        try:
            with wave.open(file_path, 'rb') as wav_file:
                audio_data = wav_file.readframes(wav_file.getnframes())
                
            # Process for STT
            processed_audio = self.audio_processor.process_for_stt(audio_data)
            return processed_audio
            
        except Exception as e:
            logger.error(f"Error processing audio file {file_path}: {e}")
            return None
            
    async def process_bytes(self, audio_data: bytes) -> Optional[bytes]:
        """Process raw audio bytes."""
        try:
            processed_audio = self.audio_processor.process_for_stt(audio_data)
            return processed_audio
        except Exception as e:
            logger.error(f"Error processing audio bytes: {e}")
            return None
