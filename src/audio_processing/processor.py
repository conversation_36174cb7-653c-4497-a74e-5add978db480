"""Audio processing components for the AI SIP Call Agent."""

import asyncio
import wave
import io
import logging
from typing import Op<PERSON>, <PERSON><PERSON>
import numpy as np

from ..call_agent.config import Config, get_config

logger = logging.getLogger(__name__)


class AudioProcessor:
    """Handles audio processing tasks."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.sample_rate = self.config.audio.sample_rate
        self.channels = self.config.audio.channels
        self.chunk_size = self.config.audio.chunk_size
        self.silence_threshold = self.config.audio.silence_threshold
        
    def convert_to_wav(self, audio_data: bytes, 
                      sample_rate: Optional[int] = None,
                      channels: Optional[int] = None) -> bytes:
        """Convert raw audio data to WAV format."""
        try:
            sample_rate = sample_rate or self.sample_rate
            channels = channels or self.channels
            
            # Create WAV file in memory
            wav_buffer = io.BytesIO()
            
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(channels)
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data)
                
            wav_buffer.seek(0)
            return wav_buffer.read()
            
        except Exception as e:
            logger.error(f"Error converting audio to WAV: {e}")
            return audio_data
            
    def detect_silence(self, audio_data: bytes) -> bool:
        """Detect if audio data contains mostly silence."""
        try:
            # Convert bytes to numpy array (assuming 16-bit PCM)
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # Calculate RMS (root mean square) energy
            rms = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))
            
            # Normalize to 0-1 range
            normalized_rms = rms / 32768.0
            
            return normalized_rms < self.silence_threshold
            
        except Exception as e:
            logger.error(f"Error detecting silence: {e}")
            return False
            
    def trim_silence(self, audio_data: bytes) -> bytes:
        """Remove silence from the beginning and end of audio."""
        try:
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # Calculate energy for each frame
            frame_size = self.chunk_size // 2  # 16-bit samples
            frames = len(audio_array) // frame_size
            
            start_frame = 0
            end_frame = frames
            
            # Find first non-silent frame
            for i in range(frames):
                frame_start = i * frame_size
                frame_end = min((i + 1) * frame_size, len(audio_array))
                frame_data = audio_array[frame_start:frame_end]
                
                rms = np.sqrt(np.mean(frame_data.astype(np.float32) ** 2))
                normalized_rms = rms / 32768.0
                
                if normalized_rms >= self.silence_threshold:
                    start_frame = i
                    break
                    
            # Find last non-silent frame
            for i in range(frames - 1, -1, -1):
                frame_start = i * frame_size
                frame_end = min((i + 1) * frame_size, len(audio_array))
                frame_data = audio_array[frame_start:frame_end]
                
                rms = np.sqrt(np.mean(frame_data.astype(np.float32) ** 2))
                normalized_rms = rms / 32768.0
                
                if normalized_rms >= self.silence_threshold:
                    end_frame = i + 1
                    break
                    
            # Extract non-silent portion
            start_sample = start_frame * frame_size
            end_sample = end_frame * frame_size
            
            trimmed_array = audio_array[start_sample:end_sample]
            return trimmed_array.tobytes()
            
        except Exception as e:
            logger.error(f"Error trimming silence: {e}")
            return audio_data
            
    def resample_audio(self, audio_data: bytes, 
                      source_rate: int, target_rate: int) -> bytes:
        """Resample audio to target sample rate."""
        if source_rate == target_rate:
            return audio_data
            
        try:
            # Convert to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # Calculate resampling ratio
            ratio = target_rate / source_rate
            
            # Simple linear interpolation resampling
            # For production, consider using scipy.signal.resample
            original_length = len(audio_array)
            new_length = int(original_length * ratio)
            
            # Create new indices
            old_indices = np.linspace(0, original_length - 1, new_length)
            
            # Interpolate
            resampled = np.interp(old_indices, np.arange(original_length), audio_array)
            
            return resampled.astype(np.int16).tobytes()
            
        except Exception as e:
            logger.error(f"Error resampling audio: {e}")
            return audio_data
            
    def normalize_audio(self, audio_data: bytes) -> bytes:
        """Normalize audio volume."""
        try:
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # Find peak amplitude
            peak = np.max(np.abs(audio_array))
            
            if peak == 0:
                return audio_data
                
            # Normalize to use full 16-bit range (with some headroom)
            target_peak = 32767 * 0.9  # 90% of max to avoid clipping
            scale_factor = target_peak / peak
            
            normalized = audio_array * scale_factor
            normalized = np.clip(normalized, -32768, 32767)
            
            return normalized.astype(np.int16).tobytes()
            
        except Exception as e:
            logger.error(f"Error normalizing audio: {e}")
            return audio_data
            
    def process_for_stt(self, audio_data: bytes) -> bytes:
        """Process audio for speech-to-text (Whisper)."""
        # Trim silence
        processed = self.trim_silence(audio_data)
        
        # Normalize volume
        processed = self.normalize_audio(processed)
        
        # Convert to WAV format
        processed = self.convert_to_wav(processed)
        
        return processed
        
    def process_for_playback(self, audio_data: bytes) -> bytes:
        """Process audio for playback through SIP."""
        # Normalize volume
        processed = self.normalize_audio(audio_data)
        
        # Ensure correct sample rate
        # Note: You might need to detect the source sample rate
        # For now, assume TTS returns audio at the correct rate
        
        return processed


class VoiceActivityDetector:
    """Simple voice activity detection."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.silence_threshold = self.config.audio.silence_threshold
        self.silence_duration = self.config.audio.silence_duration
        self.sample_rate = self.config.audio.sample_rate
        
        # State tracking
        self.silence_start_time = None
        self.is_speaking = False
        
    def process_chunk(self, audio_chunk: bytes, timestamp: float) -> Tuple[bool, bool]:
        """
        Process an audio chunk and return (is_speech, should_process).
        
        Returns:
            is_speech: True if chunk contains speech
            should_process: True if accumulated audio should be processed
        """
        # Detect if chunk contains speech
        audio_array = np.frombuffer(audio_chunk, dtype=np.int16)
        rms = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))
        normalized_rms = rms / 32768.0
        
        is_speech = normalized_rms >= self.silence_threshold
        
        if is_speech:
            # Speech detected
            self.silence_start_time = None
            if not self.is_speaking:
                self.is_speaking = True
                logger.debug("Speech started")
        else:
            # Silence detected
            if self.is_speaking and self.silence_start_time is None:
                self.silence_start_time = timestamp
                
        # Check if we should process accumulated audio
        should_process = False
        if (self.is_speaking and 
            self.silence_start_time is not None and 
            timestamp - self.silence_start_time >= self.silence_duration):
            # End of speech detected
            should_process = True
            self.is_speaking = False
            self.silence_start_time = None
            logger.debug("Speech ended, should process")
            
        return is_speech, should_process
        
    def reset(self):
        """Reset VAD state."""
        self.silence_start_time = None
        self.is_speaking = False
