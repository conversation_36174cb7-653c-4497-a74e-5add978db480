"""Prompt engineering and management for the AI SIP Call Agent."""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

from .config import Config, get_config
from .goals import GoalManager, Goal

logger = logging.getLogger(__name__)


@dataclass
class PromptTemplate:
    """Template for generating prompts."""
    name: str
    template: str
    variables: List[str]
    description: str = ""


class PromptManager:
    """Manages prompt templates and generation for the AI agent."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.templates = self._load_default_templates()
        
    def _load_default_templates(self) -> Dict[str, PromptTemplate]:
        """Load default prompt templates."""
        return {
            "system_base": PromptTemplate(
                name="system_base",
                template="""You are {agent_name}, a helpful AI assistant answering phone calls.

Your personality: {personality}

Current date and time: {current_time}

IMPORTANT GUIDELINES:
- Keep responses concise and conversational (1-3 sentences max)
- Speak naturally as if you're having a phone conversation
- Ask clarifying questions when needed
- Be polite, professional, and empathetic
- If you don't know something, admit it and offer to help find the answer
- Avoid technical jargon unless specifically relevant
- Remember this is a voice conversation, so avoid formatting or lists

CONVERSATION GOALS:
{goals_text}

CONTEXT:
- This is a phone call conversation
- Respond as if speaking directly to the caller
- Keep the conversation flowing naturally""",
                variables=["agent_name", "personality", "current_time", "goals_text"],
                description="Base system prompt for the AI agent"
            ),
            
            "greeting": PromptTemplate(
                name="greeting",
                template="""Hello! This is {agent_name}, your AI assistant. How can I help you today?""",
                variables=["agent_name"],
                description="Initial greeting for new calls"
            ),
            
            "clarification": PromptTemplate(
                name="clarification",
                template="""I want to make sure I understand correctly. Could you tell me a bit more about {topic}?""",
                variables=["topic"],
                description="Request for clarification"
            ),
            
            "information_gathering": PromptTemplate(
                name="information_gathering",
                template="""To better assist you, could you please provide your {info_type}? This will help me {reason}.""",
                variables=["info_type", "reason"],
                description="Gather specific information from caller"
            ),
            
            "assistance_offer": PromptTemplate(
                name="assistance_offer",
                template="""Based on what you've told me about {issue}, here's how I can help: {solution}. Would you like me to {action}?""",
                variables=["issue", "solution", "action"],
                description="Offer specific assistance"
            ),
            
            "closure": PromptTemplate(
                name="closure",
                template="""Is there anything else I can help you with today? If you need further assistance, feel free to call back anytime.""",
                variables=[],
                description="Professional call closure"
            ),
            
            "error_recovery": PromptTemplate(
                name="error_recovery",
                template="""I apologize, but I didn't quite catch that. Could you please repeat what you said?""",
                variables=[],
                description="Recovery from transcription errors"
            ),
            
            "hold_message": PromptTemplate(
                name="hold_message",
                template="""Please hold on for just a moment while I {action}. I'll be right back with you.""",
                variables=["action"],
                description="Message when processing takes time"
            )
        }
        
    def get_template(self, name: str) -> Optional[PromptTemplate]:
        """Get a specific prompt template."""
        return self.templates.get(name)
        
    def add_template(self, template: PromptTemplate):
        """Add a new prompt template."""
        self.templates[template.name] = template
        logger.info(f"Added prompt template: {template.name}")
        
    def generate_system_prompt(self, goals: List[Goal], 
                             conversation_context: Optional[Dict[str, Any]] = None) -> str:
        """Generate the system prompt for the AI agent."""
        template = self.get_template("system_base")
        if not template:
            return self.config.agent.system_prompt
            
        # Format goals text
        goals_text = self._format_goals_for_prompt(goals)
        
        # Get current time
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Generate prompt
        try:
            prompt = template.template.format(
                agent_name=self.config.agent.name,
                personality=self.config.agent.personality,
                current_time=current_time,
                goals_text=goals_text
            )
            return prompt
        except KeyError as e:
            logger.error(f"Error formatting system prompt: {e}")
            return self.config.agent.system_prompt
            
    def _format_goals_for_prompt(self, goals: List[Goal]) -> str:
        """Format goals for inclusion in prompts."""
        if not goals:
            return "- Provide helpful assistance to the caller"
            
        goal_lines = []
        for goal in goals:
            if goal.priority <= 2:  # Only include high priority goals
                goal_lines.append(f"- {goal.description}")
                
        return "\n".join(goal_lines) if goal_lines else "- Provide helpful assistance to the caller"
        
    def generate_contextual_prompt(self, template_name: str, 
                                 context: Dict[str, Any]) -> Optional[str]:
        """Generate a prompt from a template with context."""
        template = self.get_template(template_name)
        if not template:
            logger.warning(f"Template not found: {template_name}")
            return None
            
        try:
            return template.template.format(**context)
        except KeyError as e:
            logger.error(f"Missing variable for template {template_name}: {e}")
            return None
            
    def suggest_response_type(self, caller_text: str, 
                            conversation_history: List[Dict[str, str]]) -> str:
        """Suggest the most appropriate response type based on context."""
        caller_lower = caller_text.lower()
        
        # Check for greeting patterns
        if any(word in caller_lower for word in ["hello", "hi", "hey"]):
            if len(conversation_history) <= 2:  # Early in conversation
                return "greeting"
                
        # Check for clarification needs
        if any(phrase in caller_lower for phrase in ["what", "how", "why", "explain"]):
            return "clarification"
            
        # Check for information sharing
        if any(phrase in caller_lower for phrase in ["my name is", "i'm", "i am"]):
            return "information_gathering"
            
        # Check for problem statements
        if any(word in caller_lower for word in ["problem", "issue", "help", "need"]):
            return "assistance_offer"
            
        # Check for closure signals
        if any(phrase in caller_lower for phrase in ["thank you", "thanks", "goodbye", "that's all"]):
            return "closure"
            
        # Default to general assistance
        return "general"
        
    def enhance_response_with_goals(self, base_response: str, 
                                  active_goals: List[Goal],
                                  conversation_context: Dict[str, Any]) -> str:
        """Enhance a response to better align with active goals."""
        # This is a simplified implementation
        # In production, you'd want more sophisticated goal-driven response modification
        
        enhanced_response = base_response
        
        # Add goal-specific enhancements
        for goal in active_goals:
            if goal.id == "gather_contact_info" and goal.progress < 0.5:
                if "name" not in conversation_context.get("collected_info", {}):
                    enhanced_response += " By the way, may I have your name for our records?"
                    
            elif goal.id == "understand_intent" and goal.progress < 0.5:
                if "?" not in enhanced_response:
                    enhanced_response += " Could you tell me more about what specifically you need help with?"
                    
        return enhanced_response
        
    def get_fallback_responses(self) -> List[str]:
        """Get fallback responses for when the AI is uncertain."""
        return [
            "I want to make sure I give you the best help possible. Could you rephrase that for me?",
            "I'm not entirely sure I understood. Could you tell me more about what you need?",
            "Let me make sure I'm following you correctly. Are you asking about...?",
            "I want to help you with this. Could you give me a bit more detail?",
            "I'm here to help. Could you explain that in a different way?"
        ]
        
    def generate_error_recovery_prompt(self, error_type: str) -> str:
        """Generate appropriate error recovery prompts."""
        error_prompts = {
            "transcription_error": "I'm sorry, I didn't catch that clearly. Could you please repeat what you said?",
            "processing_timeout": "I apologize for the delay. Let me try to help you with that again.",
            "service_unavailable": "I'm experiencing some technical difficulties. Let me try a different approach to help you.",
            "unclear_intent": "I want to make sure I understand what you need. Could you tell me more about how I can help?",
            "general_error": "I apologize for any confusion. Let me try to assist you better. What can I help you with?"
        }
        
        return error_prompts.get(error_type, error_prompts["general_error"])
        
    def customize_for_caller(self, base_prompt: str, 
                           caller_info: Dict[str, Any]) -> str:
        """Customize prompts based on caller information."""
        # Simple customization based on caller info
        customized = base_prompt
        
        if caller_info.get("is_returning_caller"):
            customized = customized.replace("How can I help you today?", 
                                          "Welcome back! How can I help you today?")
            
        if caller_info.get("preferred_name"):
            name = caller_info["preferred_name"]
            customized = customized.replace("you", f"you, {name}")
            
        return customized
