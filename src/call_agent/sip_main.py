"""
Main entry point that integrates the AI Call Agent with the working SIP client.
This version ensures proper SIP registration and call handling.
"""

import asyncio
import logging
import signal
import sys
import os
import threading
import time
from pathlib import Path

# Add the project root to the path so we can import the working SIP client
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "ingridients"))

from .config import get_config, reload_config
from .agent import CallAgentService
from ..web_interface.app import create_web_app

# Import the working SIP client
try:
    import sip_audio_session3
except ImportError as e:
    logging.error(f"Could not import working SIP client: {e}")
    logging.error("Make sure sip-audio-session3.py is in the ingridients directory")
    sys.exit(1)


def setup_logging(config):
    """Setup logging configuration."""
    # Create logs directory
    log_file = Path(config.logging.file)
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, config.logging.level.upper()),
        format=config.logging.format,
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set specific logger levels
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)


class IntegratedSIPCallAgent:
    """Integrated SIP Call Agent that combines the working SIP client with AI processing."""
    
    def __init__(self):
        self.config = get_config()
        self.call_agent = None
        self.web_app = None
        self.web_server = None
        self.sip_app = None
        self.shutdown_event = asyncio.Event()
        
        # Threading for SIP client (which uses twisted reactor)
        self.sip_thread = None
        
    async def start(self):
        """Start the integrated application."""
        logger = logging.getLogger(__name__)
        logger.info("Starting Integrated AI SIP Call Agent...")
        
        try:
            # Initialize call agent service
            self.call_agent = CallAgentService(self.config)
            await self.call_agent.start()
            
            # Create and start web interface
            self.web_app = create_web_app(self.call_agent)
            
            # Start web server
            import uvicorn
            config = uvicorn.Config(
                self.web_app,
                host=self.config.web.host,
                port=self.config.web.port,
                log_level=self.config.logging.level.lower(),
                access_log=True
            )
            
            self.web_server = uvicorn.Server(config)
            
            # Start web server in background
            web_task = asyncio.create_task(self.web_server.serve())
            
            logger.info(f"Web interface available at http://{self.config.web.host}:{self.config.web.port}")
            
            # Start SIP client in a separate thread
            self._start_sip_client()
            
            logger.info("Integrated AI SIP Call Agent started successfully")
            logger.info("SIP client is running and should be registered")
            logger.info("The system will handle incoming calls with AI processing")
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
            # Graceful shutdown
            logger.info("Shutting down...")
            
            # Stop SIP client
            if self.sip_app:
                try:
                    self.sip_app.stop()
                except:
                    pass
                    
            # Stop web server
            if self.web_server:
                self.web_server.should_exit = True
                await web_task
                
            # Stop call agent
            if self.call_agent:
                await self.call_agent.stop()
                
            logger.info("Application shutdown complete")
            
        except Exception as e:
            logger.error(f"Application startup failed: {e}")
            raise
            
    def _start_sip_client(self):
        """Start the SIP client in a separate thread."""
        logger = logging.getLogger(__name__)
        
        def run_sip_client():
            """Run the SIP client with AI integration."""
            try:
                # Import and modify the working SIP application
                from sip_audio_session3 import SIPAudioSession
                
                # Create a custom SIP application that integrates with our AI agent
                class AISIPAudioSession(SIPAudioSession):
                    def __init__(self, call_agent_service):
                        super().__init__()
                        self.call_agent = call_agent_service
                        self.active_calls = {}
                        
                    def _NH_SIPSessionNewIncoming(self, notification):
                        """Override to add AI processing."""
                        session = notification.sender
                        
                        # Get caller information
                        caller_id = str(session.remote_identity.uri)
                        if session.remote_identity.display_name:
                            caller_id = f"{session.remote_identity.display_name} <{caller_id}>"
                            
                        logger.info(f"AI: Incoming call from: {caller_id}")
                        
                        # Start AI call session
                        try:
                            # Create new event loop for this thread
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            
                            call_id = loop.run_until_complete(
                                self.call_agent.start_call(caller_id)
                            )
                            
                            self.active_calls[id(session)] = call_id
                            logger.info(f"AI: Started call session {call_id}")
                            
                            loop.close()
                            
                        except Exception as e:
                            logger.error(f"AI: Failed to start call session: {e}")
                            
                        # Call the original handler
                        super()._NH_SIPSessionNewIncoming(notification)
                        
                    def _NH_SIPSessionDidEnd(self, notification):
                        """Override to clean up AI session."""
                        session = notification.sender
                        session_id = id(session)
                        
                        if session_id in self.active_calls:
                            call_id = self.active_calls[session_id]
                            
                            try:
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)
                                
                                loop.run_until_complete(
                                    self.call_agent.end_call(call_id, "session_ended")
                                )
                                
                                loop.close()
                                logger.info(f"AI: Ended call session {call_id}")
                                
                            except Exception as e:
                                logger.error(f"AI: Failed to end call session: {e}")
                                
                            del self.active_calls[session_id]
                            
                        # Call the original handler
                        super()._NH_SIPSessionDidEnd(notification)
                
                # Create and run the AI-integrated SIP application
                self.sip_app = AISIPAudioSession(self.call_agent)
                
                # Use the same configuration directory as the working client
                config_directory = os.path.expanduser('~/.sipclient')
                
                logger.info("Starting SIP client with AI integration...")
                logger.info(f"Using config directory: {config_directory}")
                
                # This will start the SIP client and block until stopped
                self.sip_app.run(config_directory)
                
            except Exception as e:
                logger.error(f"SIP client error: {e}")
                import traceback
                traceback.print_exc()
        
        # Start SIP client in a separate thread
        self.sip_thread = threading.Thread(target=run_sip_client, daemon=True)
        self.sip_thread.start()
        
        logger.info("SIP client thread started")
        
    def shutdown(self):
        """Signal shutdown."""
        self.shutdown_event.set()


def signal_handler(app: IntegratedSIPCallAgent):
    """Handle shutdown signals."""
    def handler(signum, frame):
        logging.getLogger(__name__).info(f"Received signal {signum}, shutting down...")
        app.shutdown()
    return handler


async def main():
    """Main entry point."""
    # Load configuration
    config = get_config()
    
    # Setup logging
    setup_logging(config)
    
    logger = logging.getLogger(__name__)
    logger.info("Integrated AI SIP Call Agent starting...")
    
    # Create application
    app = IntegratedSIPCallAgent()
    
    # Setup signal handlers
    handler = signal_handler(app)
    signal.signal(signal.SIGINT, handler)
    signal.signal(signal.SIGTERM, handler)
    
    try:
        await app.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
