"""Configuration management for the AI SIP Call Agent."""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv


class SIPConfig(BaseModel):
    """SIP configuration settings."""
    account: Optional[str] = None
    auto_answer: bool = True
    auto_answer_interval: int = 2
    enable_recording: bool = True
    recording_dir: str = "./recordings"


class ServiceConfig(BaseModel):
    """External service configuration."""
    base_url: str
    timeout: int = 30


class WhisperConfig(ServiceConfig):
    """Whisper STT service configuration."""
    base_url: str = "http://***************:8080"
    inference_path: str = "/inference"


class KokoroTTSConfig(ServiceConfig):
    """Kokoro TTS service configuration."""
    base_url: str = "http://***************:8081"
    tts_path: str = "/tts"
    audio_path: str = "/audio"
    voice: str = "af_heart"
    speed: float = 1.0


class LiteLLMConfig(ServiceConfig):
    """LiteLLM service configuration."""
    base_url: str = "https://litellm.xn--8pr.xyz/"
    api_key: str = "sk-1337"
    model: str = "ollama/gemma3:4b"


class ServicesConfig(BaseModel):
    """All external services configuration."""
    whisper: WhisperConfig = Field(default_factory=WhisperConfig)
    kokoro_tts: KokoroTTSConfig = Field(default_factory=KokoroTTSConfig)
    litellm: LiteLLMConfig = Field(default_factory=LiteLLMConfig)


class AudioConfig(BaseModel):
    """Audio processing configuration."""
    chunk_size: int = 1024
    sample_rate: int = 16000
    channels: int = 1
    format: str = "wav"
    silence_threshold: float = 0.01
    silence_duration: float = 2.0
    max_recording_duration: float = 30.0


class AgentConfig(BaseModel):
    """Call agent behavior configuration."""
    name: str = "AI Assistant"
    personality: str = "helpful and professional"
    max_conversation_turns: int = 50
    conversation_timeout: int = 300
    system_prompt: str = """You are a helpful AI assistant answering phone calls. 
Keep responses concise and conversational.
Ask clarifying questions when needed.
Be polite and professional."""
    goals: list[str] = Field(default_factory=lambda: [
        "Assist callers with their questions",
        "Gather contact information if appropriate", 
        "Provide helpful information",
        "Maintain a friendly conversation"
    ])


class WebConfig(BaseModel):
    """Web interface configuration."""
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = True


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "./logs/call_agent.log"
    max_file_size: str = "10MB"
    backup_count: int = 5


class StorageConfig(BaseModel):
    """Storage configuration."""
    conversations_dir: str = "./conversations"
    transcripts_dir: str = "./transcripts"
    audio_cache_dir: str = "./audio_cache"
    max_cache_size: str = "1GB"
    cleanup_after_days: int = 30


class Config(BaseModel):
    """Main configuration class."""
    sip: SIPConfig = Field(default_factory=SIPConfig)
    services: ServicesConfig = Field(default_factory=ServicesConfig)
    audio: AudioConfig = Field(default_factory=AudioConfig)
    agent: AgentConfig = Field(default_factory=AgentConfig)
    web: WebConfig = Field(default_factory=WebConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    storage: StorageConfig = Field(default_factory=StorageConfig)


def load_config(config_path: str = "config.yaml") -> Config:
    """Load configuration from YAML file and environment variables."""
    # Load environment variables
    load_dotenv()
    
    # Load YAML config if it exists
    config_data = {}
    if Path(config_path).exists():
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f) or {}
    
    # Apply environment variable overrides
    env_overrides = _get_env_overrides()
    config_data = _merge_configs(config_data, env_overrides)
    
    return Config(**config_data)


def _get_env_overrides() -> Dict[str, Any]:
    """Get configuration overrides from environment variables."""
    overrides = {}
    
    # Service URLs
    if whisper_url := os.getenv("WHISPER_BASE_URL"):
        overrides.setdefault("services", {}).setdefault("whisper", {})["base_url"] = whisper_url
    
    if tts_url := os.getenv("KOKORO_TTS_BASE_URL"):
        overrides.setdefault("services", {}).setdefault("kokoro_tts", {})["base_url"] = tts_url
    
    if llm_url := os.getenv("LITELLM_BASE_URL"):
        overrides.setdefault("services", {}).setdefault("litellm", {})["base_url"] = llm_url
    
    if llm_key := os.getenv("LITELLM_API_KEY"):
        overrides.setdefault("services", {}).setdefault("litellm", {})["api_key"] = llm_key
    
    # Web interface
    if web_host := os.getenv("WEB_HOST"):
        overrides.setdefault("web", {})["host"] = web_host
    
    if web_port := os.getenv("WEB_PORT"):
        overrides.setdefault("web", {})["port"] = int(web_port)
    
    if web_debug := os.getenv("WEB_DEBUG"):
        overrides.setdefault("web", {})["debug"] = web_debug.lower() == "true"
    
    # Logging
    if log_level := os.getenv("LOG_LEVEL"):
        overrides.setdefault("logging", {})["level"] = log_level
    
    if log_file := os.getenv("LOG_FILE"):
        overrides.setdefault("logging", {})["file"] = log_file
    
    return overrides


def _merge_configs(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
    """Recursively merge configuration dictionaries."""
    result = base.copy()
    
    for key, value in override.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = _merge_configs(result[key], value)
        else:
            result[key] = value
    
    return result


# Global configuration instance
config: Optional[Config] = None


def get_config() -> Config:
    """Get the global configuration instance."""
    global config
    if config is None:
        config = load_config()
    return config


def reload_config(config_path: str = "config.yaml") -> Config:
    """Reload configuration from file."""
    global config
    config = load_config(config_path)
    return config
