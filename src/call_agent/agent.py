"""Main Call Agent Service - Central orchestrator for AI SIP calls."""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, Callable
from datetime import datetime
import uuid

from .config import Config, get_config
from .services import ServiceManager
from .conversation import ConversationManager, Conversation
from .goals import GoalManager
from .prompts import PromptManager
from ..audio_processing.processor import AudioProcessor

logger = logging.getLogger(__name__)


class CallAgentService:
    """Central orchestrator for AI SIP call handling."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.services = ServiceManager(self.config)
        self.conversation_manager = ConversationManager(self.config)
        self.audio_processor = AudioProcessor(self.config)
        self.goal_manager = GoalManager()
        self.prompt_manager = PromptManager(self.config)

        # Load default goals
        self.goal_manager.load_default_goals()

        # Active calls tracking
        self.active_calls: Dict[str, 'CallSession'] = {}

        # Event callbacks
        self.on_call_started: Optional[Callable] = None
        self.on_call_ended: Optional[Callable] = None
        self.on_transcript_update: Optional[Callable] = None

        self.is_running = False
        
    async def start(self):
        """Start the call agent service."""
        logger.info("Starting Call Agent Service...")
        
        # Check service health
        health = await self.services.health_check()
        logger.info(f"Service health check: {health}")
        
        if not all(health.values()):
            logger.warning("Some external services are not available")
            
        self.is_running = True
        logger.info("Call Agent Service started successfully")
        
    async def stop(self):
        """Stop the call agent service."""
        logger.info("Stopping Call Agent Service...")
        
        # End all active calls
        for call_id in list(self.active_calls.keys()):
            await self.end_call(call_id, "service_shutdown")
            
        self.is_running = False
        logger.info("Call Agent Service stopped")
        
    async def start_call(self, caller_id: str, call_id: Optional[str] = None) -> str:
        """Start a new call session."""
        if call_id is None:
            call_id = str(uuid.uuid4())
            
        logger.info(f"Starting call {call_id} from caller {caller_id}")
        
        # Create conversation
        conversation = self.conversation_manager.create_conversation(caller_id)
        
        # Create call session
        call_session = CallSession(
            call_id=call_id,
            caller_id=caller_id,
            conversation=conversation,
            agent_service=self
        )
        
        self.active_calls[call_id] = call_session
        
        # Notify callback
        if self.on_call_started:
            await self.on_call_started(call_session.get_status())
            
        logger.info(f"Call {call_id} started successfully")
        return call_id
        
    async def end_call(self, call_id: str, reason: str = "normal"):
        """End a call session."""
        call_session = self.active_calls.get(call_id)
        if not call_session:
            logger.warning(f"Attempted to end non-existent call {call_id}")
            return
            
        logger.info(f"Ending call {call_id} - reason: {reason}")
        
        # End conversation
        self.conversation_manager.end_conversation(
            call_session.conversation.metadata.conversation_id,
            "completed" if reason == "normal" else reason
        )
        
        # Clean up call session
        call_session.is_active = False
        del self.active_calls[call_id]
        
        # Notify callback
        if self.on_call_ended:
            await self.on_call_ended(call_session.get_status())
            
        logger.info(f"Call {call_id} ended")
        
    async def process_audio(self, call_id: str, audio_data: bytes) -> Optional[bytes]:
        """Process incoming audio from a call."""
        call_session = self.active_calls.get(call_id)
        if not call_session:
            logger.warning(f"Received audio for non-existent call {call_id}")
            return None
            
        return await call_session.process_audio(audio_data)
        
    def get_call_status(self, call_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific call."""
        call_session = self.active_calls.get(call_id)
        if call_session:
            return call_session.get_status()
        return None
        
    def get_all_calls_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all active calls."""
        return {
            call_id: session.get_status() 
            for call_id, session in self.active_calls.items()
        }


class CallSession:
    """Represents an active call session."""
    
    def __init__(self, call_id: str, caller_id: str, conversation: Conversation, 
                 agent_service: CallAgentService):
        self.call_id = call_id
        self.caller_id = caller_id
        self.conversation = conversation
        self.agent_service = agent_service
        self.start_time = datetime.now()
        self.is_active = True
        
        # Audio processing state
        self.audio_buffer = bytearray()
        self.is_processing_audio = False
        self.last_speech_time = time.time()
        
    async def process_audio(self, audio_data: bytes) -> Optional[bytes]:
        """Process incoming audio and generate response."""
        if not self.is_active or self.is_processing_audio:
            return None
            
        # Add to buffer
        self.audio_buffer.extend(audio_data)
        
        # Check if we have enough audio or silence detected
        if not self._should_process_audio():
            return None
            
        self.is_processing_audio = True
        start_time = time.time()
        
        try:
            # Extract audio from buffer
            audio_to_process = bytes(self.audio_buffer)
            self.audio_buffer.clear()
            
            # Transcribe speech
            transcript = await self.agent_service.services.whisper.transcribe_audio(audio_to_process)
            if not transcript:
                return None
                
            # Add caller turn to conversation
            self.conversation.add_turn("caller", transcript)
            
            # Generate AI response with goal-aware prompting
            context_messages = self.conversation.get_context_messages()

            # Get active goals for this conversation
            active_goals = self.agent_service.goal_manager.get_active_goals()

            # Update system prompt with current goals
            if context_messages and context_messages[0]["role"] == "system":
                enhanced_system_prompt = self.agent_service.prompt_manager.generate_system_prompt(active_goals)
                context_messages[0]["content"] = enhanced_system_prompt

            ai_response = await self.agent_service.services.llm.generate_response(context_messages)

            if not ai_response:
                ai_response = self.agent_service.prompt_manager.generate_error_recovery_prompt("general_error")
                
            # Add agent turn to conversation
            processing_time = time.time() - start_time
            self.conversation.add_turn("agent", ai_response, processing_time=processing_time)
            
            # Generate speech
            response_audio = await self.agent_service.services.tts.synthesize_speech(ai_response)
            
            # Notify transcript update
            if self.agent_service.on_transcript_update:
                await self.agent_service.on_transcript_update({
                    "call_id": self.call_id,
                    "caller_text": transcript,
                    "agent_text": ai_response,
                    "processing_time": processing_time
                })
                
            logger.info(f"Processed audio for call {self.call_id}: '{transcript}' -> '{ai_response}'")
            return response_audio
            
        except Exception as e:
            logger.error(f"Error processing audio for call {self.call_id}: {e}")
            return None
        finally:
            self.is_processing_audio = False
            
    def _should_process_audio(self) -> bool:
        """Determine if accumulated audio should be processed."""
        # Simple implementation - process when buffer reaches certain size
        # In a real implementation, you'd want voice activity detection
        min_buffer_size = self.agent_service.config.audio.chunk_size * 10
        return len(self.audio_buffer) >= min_buffer_size
        
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the call session."""
        duration = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "call_id": self.call_id,
            "caller_id": self.caller_id,
            "conversation_id": self.conversation.metadata.conversation_id,
            "start_time": self.start_time.isoformat(),
            "duration_seconds": duration,
            "is_active": self.is_active,
            "is_processing": self.is_processing_audio,
            "total_turns": self.conversation.metadata.total_turns,
            "goals_achieved": self.conversation.metadata.goals_achieved,
            "buffer_size": len(self.audio_buffer)
        }
