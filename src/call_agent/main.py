"""Main entry point for the AI SIP Call Agent Service."""

import asyncio
import logging
import signal
import sys
from pathlib import Path

from .config import get_config, reload_config
from .agent import CallAgentService
from ..web_interface.app import create_web_app


def setup_logging(config):
    """Setup logging configuration."""
    # Create logs directory
    log_file = Path(config.logging.file)
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, config.logging.level.upper()),
        format=config.logging.format,
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set specific logger levels
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)


class CallAgentApplication:
    """Main application class."""
    
    def __init__(self):
        self.config = get_config()
        self.call_agent = None
        self.web_app = None
        self.web_server = None
        self.shutdown_event = asyncio.Event()
        
    async def start(self):
        """Start the application."""
        logger = logging.getLogger(__name__)
        logger.info("Starting AI SIP Call Agent Application...")
        
        try:
            # Initialize call agent service
            self.call_agent = CallAgentService(self.config)
            await self.call_agent.start()
            
            # Create and start web interface
            self.web_app = create_web_app(self.call_agent)
            
            # Start web server
            import uvicorn
            config = uvicorn.Config(
                self.web_app,
                host=self.config.web.host,
                port=self.config.web.port,
                log_level=self.config.logging.level.lower(),
                access_log=True
            )
            
            self.web_server = uvicorn.Server(config)
            
            # Start web server in background
            web_task = asyncio.create_task(self.web_server.serve())
            
            logger.info(f"Web interface available at http://{self.config.web.host}:{self.config.web.port}")
            logger.info("AI SIP Call Agent Application started successfully")
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
            # Graceful shutdown
            logger.info("Shutting down...")
            
            # Stop web server
            if self.web_server:
                self.web_server.should_exit = True
                await web_task
                
            # Stop call agent
            if self.call_agent:
                await self.call_agent.stop()
                
            logger.info("Application shutdown complete")
            
        except Exception as e:
            logger.error(f"Application startup failed: {e}")
            raise
            
    def shutdown(self):
        """Signal shutdown."""
        self.shutdown_event.set()


def signal_handler(app: CallAgentApplication):
    """Handle shutdown signals."""
    def handler(signum, frame):
        logging.getLogger(__name__).info(f"Received signal {signum}, shutting down...")
        app.shutdown()
    return handler


async def main():
    """Main entry point."""
    # Load configuration
    config = get_config()
    
    # Setup logging
    setup_logging(config)
    
    logger = logging.getLogger(__name__)
    logger.info("AI SIP Call Agent starting...")
    
    # Create application
    app = CallAgentApplication()
    
    # Setup signal handlers
    handler = signal_handler(app)
    signal.signal(signal.SIGINT, handler)
    signal.signal(signal.SIGTERM, handler)
    
    try:
        await app.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
