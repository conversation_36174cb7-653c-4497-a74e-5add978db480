"""Conversation management for the AI SIP Call Agent."""

import asyncio
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

from .config import Config, get_config

logger = logging.getLogger(__name__)


@dataclass
class ConversationTurn:
    """Represents a single turn in a conversation."""
    timestamp: datetime
    speaker: str  # 'caller' or 'agent'
    content: str
    audio_file: Optional[str] = None
    processing_time: Optional[float] = None


@dataclass
class ConversationMetadata:
    """Metadata for a conversation."""
    conversation_id: str
    caller_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    status: str = "active"  # active, completed, failed
    total_turns: int = 0
    goals_achieved: List[str] = None
    
    def __post_init__(self):
        if self.goals_achieved is None:
            self.goals_achieved = []


class ConversationManager:
    """Manages conversation state and history."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.conversations: Dict[str, 'Conversation'] = {}
        self.conversations_dir = Path(self.config.storage.conversations_dir)
        self.transcripts_dir = Path(self.config.storage.transcripts_dir)
        
        # Ensure directories exist
        self.conversations_dir.mkdir(parents=True, exist_ok=True)
        self.transcripts_dir.mkdir(parents=True, exist_ok=True)
        
    def create_conversation(self, caller_id: str) -> 'Conversation':
        """Create a new conversation."""
        conversation_id = str(uuid.uuid4())
        metadata = ConversationMetadata(
            conversation_id=conversation_id,
            caller_id=caller_id,
            start_time=datetime.now(timezone.utc)
        )
        
        conversation = Conversation(metadata, self.config)
        self.conversations[conversation_id] = conversation
        
        logger.info(f"Created new conversation {conversation_id} for caller {caller_id}")
        return conversation
        
    def get_conversation(self, conversation_id: str) -> Optional['Conversation']:
        """Get an existing conversation."""
        return self.conversations.get(conversation_id)
        
    def end_conversation(self, conversation_id: str, status: str = "completed"):
        """End a conversation and save it."""
        conversation = self.conversations.get(conversation_id)
        if conversation:
            conversation.end(status)
            self._save_conversation(conversation)
            del self.conversations[conversation_id]
            logger.info(f"Ended conversation {conversation_id} with status {status}")
            
    def _save_conversation(self, conversation: 'Conversation'):
        """Save conversation to disk."""
        try:
            # Save metadata
            metadata_file = self.conversations_dir / f"{conversation.metadata.conversation_id}.json"
            with open(metadata_file, 'w') as f:
                json.dump(asdict(conversation.metadata), f, indent=2, default=str)
                
            # Save transcript
            transcript_file = self.transcripts_dir / f"{conversation.metadata.conversation_id}.txt"
            with open(transcript_file, 'w') as f:
                f.write(conversation.get_transcript())
                
            logger.info(f"Saved conversation {conversation.metadata.conversation_id}")
        except Exception as e:
            logger.error(f"Failed to save conversation: {e}")


class Conversation:
    """Represents an active conversation."""
    
    def __init__(self, metadata: ConversationMetadata, config: Optional[Config] = None):
        self.metadata = metadata
        self.config = config or get_config()
        self.turns: List[ConversationTurn] = []
        self.context_messages: List[Dict[str, str]] = []
        self.is_active = True
        
        # Initialize with system prompt
        self._add_system_message()
        
    def _add_system_message(self):
        """Add the system prompt to the conversation context."""
        system_prompt = self.config.agent.system_prompt
        
        # Add goals to the system prompt
        if self.config.agent.goals:
            goals_text = "\n".join([f"- {goal}" for goal in self.config.agent.goals])
            system_prompt += f"\n\nYour goals for this conversation:\n{goals_text}"
            
        self.context_messages.append({
            "role": "system",
            "content": system_prompt
        })
        
    def add_turn(self, speaker: str, content: str, audio_file: Optional[str] = None, 
                processing_time: Optional[float] = None) -> ConversationTurn:
        """Add a new turn to the conversation."""
        turn = ConversationTurn(
            timestamp=datetime.now(timezone.utc),
            speaker=speaker,
            content=content,
            audio_file=audio_file,
            processing_time=processing_time
        )
        
        self.turns.append(turn)
        self.metadata.total_turns += 1
        
        # Add to context messages for LLM
        role = "user" if speaker == "caller" else "assistant"
        self.context_messages.append({
            "role": role,
            "content": content
        })
        
        # Trim context if too long
        self._trim_context()
        
        logger.info(f"Added turn to conversation {self.metadata.conversation_id}: {speaker}: {content[:50]}...")
        return turn
        
    def _trim_context(self):
        """Trim conversation context to stay within limits."""
        max_turns = self.config.agent.max_conversation_turns
        
        # Keep system message + last N turns
        if len(self.context_messages) > max_turns + 1:
            system_message = self.context_messages[0]
            recent_messages = self.context_messages[-(max_turns):]
            self.context_messages = [system_message] + recent_messages
            
    def get_context_messages(self) -> List[Dict[str, str]]:
        """Get conversation context for LLM."""
        return self.context_messages.copy()
        
    def get_transcript(self) -> str:
        """Get a human-readable transcript of the conversation."""
        lines = [
            f"Conversation ID: {self.metadata.conversation_id}",
            f"Caller ID: {self.metadata.caller_id}",
            f"Start Time: {self.metadata.start_time}",
            f"End Time: {self.metadata.end_time or 'Ongoing'}",
            f"Status: {self.metadata.status}",
            f"Total Turns: {self.metadata.total_turns}",
            "",
            "Transcript:",
            "=" * 50
        ]
        
        for turn in self.turns:
            speaker_label = "CALLER" if turn.speaker == "caller" else "AGENT"
            timestamp = turn.timestamp.strftime("%H:%M:%S")
            lines.append(f"[{timestamp}] {speaker_label}: {turn.content}")
            
            if turn.processing_time:
                lines.append(f"    (Processing time: {turn.processing_time:.2f}s)")
                
        return "\n".join(lines)
        
    def is_timeout(self) -> bool:
        """Check if conversation has timed out."""
        if not self.is_active:
            return False
            
        timeout_seconds = self.config.agent.conversation_timeout
        elapsed = (datetime.now(timezone.utc) - self.metadata.start_time).total_seconds()
        return elapsed > timeout_seconds
        
    def should_end(self) -> bool:
        """Check if conversation should end based on various criteria."""
        if not self.is_active:
            return True
            
        # Check timeout
        if self.is_timeout():
            return True
            
        # Check turn limit
        if self.metadata.total_turns >= self.config.agent.max_conversation_turns * 2:  # *2 for caller+agent turns
            return True
            
        return False
        
    def end(self, status: str = "completed"):
        """End the conversation."""
        self.is_active = False
        self.metadata.end_time = datetime.now(timezone.utc)
        self.metadata.status = status
        
    def mark_goal_achieved(self, goal: str):
        """Mark a conversation goal as achieved."""
        if goal not in self.metadata.goals_achieved:
            self.metadata.goals_achieved.append(goal)
            logger.info(f"Goal achieved in conversation {self.metadata.conversation_id}: {goal}")
            
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the conversation."""
        duration = None
        if self.metadata.end_time:
            duration = (self.metadata.end_time - self.metadata.start_time).total_seconds()
            
        return {
            "conversation_id": self.metadata.conversation_id,
            "caller_id": self.metadata.caller_id,
            "start_time": self.metadata.start_time.isoformat(),
            "end_time": self.metadata.end_time.isoformat() if self.metadata.end_time else None,
            "duration_seconds": duration,
            "status": self.metadata.status,
            "total_turns": self.metadata.total_turns,
            "goals_achieved": self.metadata.goals_achieved,
            "is_active": self.is_active
        }
