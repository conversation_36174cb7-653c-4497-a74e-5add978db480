"""Goal management and evaluation for the AI SIP Call Agent."""

import logging
import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class GoalType(Enum):
    """Types of conversation goals."""
    INFORMATION_GATHERING = "information_gathering"
    CUSTOMER_SERVICE = "customer_service"
    SALES = "sales"
    SUPPORT = "support"
    GENERAL_ASSISTANCE = "general_assistance"


class GoalStatus(Enum):
    """Status of goal achievement."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    ACHIEVED = "achieved"
    FAILED = "failed"


@dataclass
class Goal:
    """Represents a conversation goal."""
    id: str
    name: str
    description: str
    goal_type: GoalType
    priority: int = 1  # 1 = highest, 5 = lowest
    keywords: List[str] = None
    success_criteria: List[str] = None
    failure_criteria: List[str] = None
    status: GoalStatus = GoalStatus.NOT_STARTED
    progress: float = 0.0  # 0.0 to 1.0
    
    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []
        if self.success_criteria is None:
            self.success_criteria = []
        if self.failure_criteria is None:
            self.failure_criteria = []


class GoalManager:
    """Manages conversation goals and evaluates progress."""
    
    def __init__(self):
        self.goals: Dict[str, Goal] = {}
        self.default_goals = self._create_default_goals()
        
    def _create_default_goals(self) -> List[Goal]:
        """Create default conversation goals."""
        return [
            Goal(
                id="greeting",
                name="Proper Greeting",
                description="Greet the caller professionally and introduce the AI assistant",
                goal_type=GoalType.GENERAL_ASSISTANCE,
                priority=1,
                keywords=["hello", "hi", "greeting", "welcome"],
                success_criteria=[
                    "Agent introduces itself",
                    "Agent asks how it can help",
                    "Professional tone maintained"
                ]
            ),
            Goal(
                id="understand_intent",
                name="Understand Caller Intent",
                description="Identify what the caller needs or wants to accomplish",
                goal_type=GoalType.INFORMATION_GATHERING,
                priority=1,
                keywords=["need", "want", "help", "problem", "question"],
                success_criteria=[
                    "Caller's intent is identified",
                    "Clarifying questions asked if needed",
                    "Intent acknowledged by agent"
                ]
            ),
            Goal(
                id="provide_assistance",
                name="Provide Helpful Assistance",
                description="Offer relevant help or information to address the caller's needs",
                goal_type=GoalType.CUSTOMER_SERVICE,
                priority=2,
                keywords=["solution", "answer", "help", "assist", "resolve"],
                success_criteria=[
                    "Relevant information provided",
                    "Caller's questions answered",
                    "Additional help offered"
                ]
            ),
            Goal(
                id="gather_contact_info",
                name="Gather Contact Information",
                description="Collect caller's contact information if appropriate",
                goal_type=GoalType.INFORMATION_GATHERING,
                priority=3,
                keywords=["name", "phone", "email", "contact", "callback"],
                success_criteria=[
                    "Name collected",
                    "Phone number or email collected",
                    "Information confirmed"
                ]
            ),
            Goal(
                id="professional_closure",
                name="Professional Call Closure",
                description="End the call professionally with appropriate follow-up",
                goal_type=GoalType.GENERAL_ASSISTANCE,
                priority=2,
                keywords=["goodbye", "thank", "follow-up", "anything else"],
                success_criteria=[
                    "Ask if anything else needed",
                    "Provide follow-up information if needed",
                    "Professional goodbye"
                ]
            )
        ]
        
    def add_goal(self, goal: Goal):
        """Add a goal to the manager."""
        self.goals[goal.id] = goal
        logger.info(f"Added goal: {goal.name}")
        
    def remove_goal(self, goal_id: str):
        """Remove a goal from the manager."""
        if goal_id in self.goals:
            del self.goals[goal_id]
            logger.info(f"Removed goal: {goal_id}")
            
    def get_goal(self, goal_id: str) -> Optional[Goal]:
        """Get a specific goal."""
        return self.goals.get(goal_id)
        
    def get_all_goals(self) -> List[Goal]:
        """Get all goals sorted by priority."""
        return sorted(self.goals.values(), key=lambda g: g.priority)
        
    def get_active_goals(self) -> List[Goal]:
        """Get goals that are not yet achieved."""
        return [
            goal for goal in self.goals.values() 
            if goal.status not in [GoalStatus.ACHIEVED, GoalStatus.FAILED]
        ]
        
    def evaluate_conversation_turn(self, caller_text: str, agent_text: str) -> List[str]:
        """Evaluate a conversation turn against all goals."""
        achieved_goals = []
        
        for goal in self.get_active_goals():
            if self._evaluate_goal_progress(goal, caller_text, agent_text):
                goal.status = GoalStatus.ACHIEVED
                goal.progress = 1.0
                achieved_goals.append(goal.id)
                logger.info(f"Goal achieved: {goal.name}")
                
        return achieved_goals
        
    def _evaluate_goal_progress(self, goal: Goal, caller_text: str, agent_text: str) -> bool:
        """Evaluate progress on a specific goal."""
        # Simple keyword-based evaluation
        # In a production system, you'd want more sophisticated NLP analysis
        
        combined_text = f"{caller_text} {agent_text}".lower()
        
        # Check for goal-specific keywords
        keyword_matches = sum(1 for keyword in goal.keywords if keyword.lower() in combined_text)
        
        # Simple heuristics based on goal type
        if goal.goal_type == GoalType.INFORMATION_GATHERING:
            return self._evaluate_information_gathering(goal, caller_text, agent_text)
        elif goal.goal_type == GoalType.CUSTOMER_SERVICE:
            return self._evaluate_customer_service(goal, caller_text, agent_text)
        elif goal.goal_type == GoalType.GENERAL_ASSISTANCE:
            return self._evaluate_general_assistance(goal, caller_text, agent_text)
            
        # Default: consider achieved if enough keywords match
        return keyword_matches >= len(goal.keywords) * 0.5
        
    def _evaluate_information_gathering(self, goal: Goal, caller_text: str, agent_text: str) -> bool:
        """Evaluate information gathering goals."""
        agent_lower = agent_text.lower()
        caller_lower = caller_text.lower()
        
        if goal.id == "understand_intent":
            # Check if agent asks clarifying questions or acknowledges intent
            question_patterns = [r'\?', r'what.*need', r'how.*help', r'tell me more']
            acknowledgment_patterns = [r'understand', r'i see', r'got it', r'clear']
            
            has_question = any(re.search(pattern, agent_lower) for pattern in question_patterns)
            has_acknowledgment = any(re.search(pattern, agent_lower) for pattern in acknowledgment_patterns)
            
            return has_question or has_acknowledgment
            
        elif goal.id == "gather_contact_info":
            # Check if contact information is mentioned
            contact_patterns = [r'name', r'phone', r'email', r'contact', r'number']
            return any(re.search(pattern, caller_lower) for pattern in contact_patterns)
            
        return False
        
    def _evaluate_customer_service(self, goal: Goal, caller_text: str, agent_text: str) -> bool:
        """Evaluate customer service goals."""
        agent_lower = agent_text.lower()
        
        if goal.id == "provide_assistance":
            # Check if agent provides helpful responses
            helpful_patterns = [
                r'here.*how', r'you can', r'i.*help', r'solution', r'answer',
                r'recommend', r'suggest', r'try this'
            ]
            return any(re.search(pattern, agent_lower) for pattern in helpful_patterns)
            
        return False
        
    def _evaluate_general_assistance(self, goal: Goal, caller_text: str, agent_text: str) -> bool:
        """Evaluate general assistance goals."""
        agent_lower = agent_text.lower()
        
        if goal.id == "greeting":
            # Check for greeting elements
            greeting_patterns = [r'hello', r'hi', r'welcome', r'assistant', r'help you']
            return any(re.search(pattern, agent_lower) for pattern in greeting_patterns)
            
        elif goal.id == "professional_closure":
            # Check for closure elements
            closure_patterns = [
                r'anything else', r'further.*help', r'thank you', r'goodbye',
                r'have.*day', r'take care'
            ]
            return any(re.search(pattern, agent_lower) for pattern in closure_patterns)
            
        return False
        
    def get_goal_summary(self) -> Dict[str, Any]:
        """Get a summary of all goals and their status."""
        total_goals = len(self.goals)
        achieved_goals = len([g for g in self.goals.values() if g.status == GoalStatus.ACHIEVED])
        in_progress_goals = len([g for g in self.goals.values() if g.status == GoalStatus.IN_PROGRESS])
        
        return {
            "total_goals": total_goals,
            "achieved_goals": achieved_goals,
            "in_progress_goals": in_progress_goals,
            "completion_rate": achieved_goals / total_goals if total_goals > 0 else 0,
            "goals": [
                {
                    "id": goal.id,
                    "name": goal.name,
                    "status": goal.status.value,
                    "progress": goal.progress,
                    "priority": goal.priority
                }
                for goal in self.get_all_goals()
            ]
        }
        
    def reset_goals(self):
        """Reset all goals to initial state."""
        for goal in self.goals.values():
            goal.status = GoalStatus.NOT_STARTED
            goal.progress = 0.0
        logger.info("All goals reset")
        
    def load_default_goals(self):
        """Load the default set of goals."""
        self.goals.clear()
        for goal in self.default_goals:
            self.add_goal(goal)
        logger.info(f"Loaded {len(self.default_goals)} default goals")
