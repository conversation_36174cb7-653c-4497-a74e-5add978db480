"""External service clients for the AI SIP Call Agent."""

import asyncio
import aiohttp
import openai
from typing import Optional, Dict, Any
from pathlib import Path
import logging

from .config import Config, get_config

logger = logging.getLogger(__name__)


class WhisperClient:
    """Client for Whisper speech-to-text service."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.base_url = self.config.services.whisper.base_url
        self.inference_path = self.config.services.whisper.inference_path
        self.timeout = self.config.services.whisper.timeout
        
    async def transcribe_audio(self, audio_data: bytes, 
                             temperature: float = 0.0,
                             temperature_inc: float = 0.2,
                             response_format: str = "json") -> Optional[str]:
        """Transcribe audio data to text."""
        try:
            url = f"{self.base_url}{self.inference_path}"
            
            # Prepare form data
            data = aiohttp.FormData()
            data.add_field('file', audio_data, filename='audio.wav', 
                          content_type='audio/wav')
            data.add_field('temperature', str(temperature))
            data.add_field('temperature_inc', str(temperature_inc))
            data.add_field('response_format', response_format)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=data, 
                                      timeout=aiohttp.ClientTimeout(total=self.timeout)) as response:
                    if response.status == 200:
                        result = await response.json()
                        text = result.get('text', '').strip()
                        logger.info(f"Transcribed audio: {text}")
                        return text
                    else:
                        logger.error(f"Whisper API error: {response.status} - {await response.text()}")
                        return None
                        
        except asyncio.TimeoutError:
            logger.error("Whisper API timeout")
            return None
        except Exception as e:
            logger.error(f"Whisper API error: {e}")
            return None


class KokoroTTSClient:
    """Client for Kokoro text-to-speech service."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.base_url = self.config.services.kokoro_tts.base_url
        self.tts_path = self.config.services.kokoro_tts.tts_path
        self.audio_path = self.config.services.kokoro_tts.audio_path
        self.voice = self.config.services.kokoro_tts.voice
        self.speed = self.config.services.kokoro_tts.speed
        self.timeout = self.config.services.kokoro_tts.timeout
        
    async def synthesize_speech(self, text: str, 
                              voice: Optional[str] = None,
                              speed: Optional[float] = None) -> Optional[bytes]:
        """Synthesize speech from text."""
        try:
            url = f"{self.base_url}{self.tts_path}"
            
            # Prepare form data
            data = aiohttp.FormData()
            data.add_field('text', text)
            data.add_field('voice', voice or self.voice)
            data.add_field('speed', str(speed or self.speed))
            
            async with aiohttp.ClientSession() as session:
                # Generate TTS
                async with session.post(url, data=data,
                                      timeout=aiohttp.ClientTimeout(total=self.timeout)) as response:
                    if response.status == 200:
                        result = await response.json()
                        filename = result.get('filename')
                        if not filename:
                            logger.error("No filename returned from TTS service")
                            return None
                        
                        # Download the audio file
                        audio_url = f"{self.base_url}{self.audio_path}/{filename}"
                        async with session.get(audio_url,
                                             timeout=aiohttp.ClientTimeout(total=self.timeout)) as audio_response:
                            if audio_response.status == 200:
                                audio_data = await audio_response.read()
                                logger.info(f"Generated TTS audio for text: {text[:50]}...")
                                return audio_data
                            else:
                                logger.error(f"Failed to download TTS audio: {audio_response.status}")
                                return None
                    else:
                        logger.error(f"TTS API error: {response.status} - {await response.text()}")
                        return None
                        
        except asyncio.TimeoutError:
            logger.error("TTS API timeout")
            return None
        except Exception as e:
            logger.error(f"TTS API error: {e}")
            return None


class LiteLLMClient:
    """Client for LiteLLM inference service."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.client = openai.OpenAI(
            api_key=self.config.services.litellm.api_key,
            base_url=self.config.services.litellm.base_url
        )
        self.model = self.config.services.litellm.model
        self.timeout = self.config.services.litellm.timeout
        
    async def generate_response(self, messages: list[Dict[str, str]], 
                              max_tokens: int = 150,
                              temperature: float = 0.7) -> Optional[str]:
        """Generate AI response from conversation messages."""
        try:
            # Run the synchronous OpenAI call in a thread pool
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    timeout=self.timeout
                )
            )
            
            if response.choices:
                content = response.choices[0].message.content
                logger.info(f"Generated AI response: {content[:100]}...")
                return content
            else:
                logger.error("No response choices returned from LLM")
                return None
                
        except Exception as e:
            logger.error(f"LLM API error: {e}")
            return None


class ServiceManager:
    """Manager for all external services."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        self.whisper = WhisperClient(config)
        self.tts = KokoroTTSClient(config)
        self.llm = LiteLLMClient(config)
        
    async def health_check(self) -> Dict[str, bool]:
        """Check health of all external services."""
        health = {}
        
        # Check Whisper
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.whisper.base_url}/", 
                                     timeout=aiohttp.ClientTimeout(total=5)) as response:
                    health['whisper'] = response.status < 400
        except:
            health['whisper'] = False
            
        # Check TTS
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.tts.base_url}/", 
                                     timeout=aiohttp.ClientTimeout(total=5)) as response:
                    health['tts'] = response.status < 400
        except:
            health['tts'] = False
            
        # Check LLM
        try:
            test_response = await self.llm.generate_response([
                {"role": "user", "content": "Hello"}
            ], max_tokens=5)
            health['llm'] = test_response is not None
        except:
            health['llm'] = False
            
        return health
