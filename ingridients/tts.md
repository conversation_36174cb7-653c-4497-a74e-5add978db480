# API Endpoints

The server provides the following REST API endpoints:

- `POST /tts`: Generate TTS audio
  - Parameters (form data):
    - `text`: The text to convert to speech (required)
    - `voice`: Voice to use (default: "af_heart")
    - `speed`: Speech speed from 0.5 to 2.0 (default: 1.0)
  - Returns: J<PERSON><PERSON> with filename of generated audio

- `GET /audio/{filename}`: Retrieve generated audio file

- `POST /play`: Play audio directly from the server
  - Parameters (form data):
    - `filename`: The filename of the audio to play (required)
  - Returns: JSON with status and filename

- `POST /stop`: Stop any currently playing audio
  - Returns: JSON with status

- `POST /open_output_folder`: Open the output folder in the system's file explorer
  - Returns: JSON with status and path
  - Note: This feature only works when running the server locally

# Base URL
http://192.168.178.250:8081

# Example
```
curl -X POST \
     -F "text=Hello, this is a test." \
     -F "voice=af_heart" \
     -F "speed=1.0" \
     -H "Accept: application/json" \
     http://192.168.178.250:8081/tts
{"filename":"tts_e9282e86-571c-49fe-bd34-6125408bdb78.wav"}
```

```
curl -O http://192.168.178.250:8081/audio/tts_e9282e86-571c-49fe-bd34-6125408bdb78.w
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed
100 96044  100 96044    0     0   429k      0 --:--:-- --:--:-- --:--:--  428k
```
