## AI SIP Callagent Implementation Plan

This plan builds upon the provided outline and adds concrete implementation details and considerations for your weekend project.

**1. Project Overview**

* **Vision:** Create a self-hosted, local-only AI-powered SIP call agent capable of engaging in conversations with callers to achieve pre-defined goals.

**2. Source Codebase Analysis**

* **Reusable Components:**
    * **sipclients3:** Core SIP handling. Adapt call handling logic to interface with AI components.  Potentially modify for call recording/playback through web interface.

* **Required Modifications:**
    * Refactor `sip-audio-session3` to trigger speech-to-text on incoming audio and text-to-speech for outgoing responses.
    * Create an intermediary service to manage communication between SIP client, Whisper (STT), Kokoro (TTS), and Litellm (Inference).  This service will handle prompt engineering, context management, and goal-oriented conversation flow.
    * Develop a minimal web interface to monitor calls, view transcripts, and potentially control the call agent's behavior.

* **Integration Challenges:**
    * Real-time performance: Balance audio chunk size for speech recognition accuracy and TTS latency.
    * Context management:  Maintain conversational context across multiple exchanges with a caller.
    * Goal definition and evaluation: Design a simple mechanism to define call agent goals and evaluate success.

**3. New Project Architecture**

* **System Design:**
    * Caller <-> SIP Client <->  Call Agent Service <->  Whisper <-> <PERSON><PERSON>o <-> Litellm <-> Web Interface
    * The Call Agent Service acts as the central orchestrator.

* **Core Components:**
    * **Call Agent Service (New):** Python service.  Handles SIP events, routes audio to Whisper, sends text to Litellm, receives responses, routes text to Kokoro, sends audio back to SIP client.  Manages conversation context and goals.
    * **Web Interface (New):** Minimal Flask/FastAPI app.  Displays call status, transcripts, and potentially allows call recording and agent control.
    * **SIP Client (Modified):**  `sip-audio-session3` adapted to stream audio to Call Agent Service and play audio received from it.

**4. Implementation Strategy**

* **Phase 1: Setup and SIP Client:**
    * Create project repository, set up virtual environment.
    * Adapt `sip-audio-session3` (simpler) to establish and end calls, stream audio to a file.

* **Phase 2: Speech and TTS Integration:**
    * Integrate Whisper for speech-to-text. 
    * Integrate Kokoro to generate audio from text files.

* **Phase 3:  Call Agent Service and Basic LLM:**
    * Create the Call Agent Service.
    * Implement simple prompt engineering to send transcribed text to Litellm and receive a response.
    * Route responses to Kokoro for TTS.
    * Connect SIP client audio stream to Whisper, text to Litellm, and Kokoro output back to SIP client.

* **Phase 4: Web Interface:**
    * Develop a minimal web interface using Flask/FastAPI.
    * Display call status and transcripts.

**5. Testing Strategy**

* **Unit tests:** Focus on Call Agent Service logic.
* **Integration tests:** Test the entire pipeline with simulated calls (audio files or simple SIP test server).

**6. Documentation**

* Focus on documenting the Call Agent Service's API and configuration.
