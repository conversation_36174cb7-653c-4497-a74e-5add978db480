OpenAI Base URL: https://litellm.xn--8pr.xyz/
Key: sk-1337

# Example:
```
cat example.py 
import openai  # openai v1.0.0+

client = openai.OpenAI(api_key="sk-1337", base_url="https://litellm.xn--8pr.xyz/")  # Set proxy to base_url

# Define prompt
prompt = "Write a long poem about cyber insecurity"

# Llama 3.2 Model (fast)
response = client.chat.completions.create(model="ollama/gemma3:4b", messages=[{"role": "user", "content": prompt}])

print(response)
```

or with curl

```
curl https://litellm.xn--8pr.xyz/v1/chat/completions   -H "Authorization: Bearer sk-1337"   -H "Content-Type: application/json"   -d '{
    "model": "ollama/gemma3:4b",
    "messages": [
      { "role": "user", "content": "What is 2+2?" }
    ]
  }'
{"id":"chatcmpl-8f181bcb-8013-4eca-b8ad-abcfda1c9a2d","created":1750550033,"model":"ollama/gemma3:4b","object":"chat.completion","system_fingerprint":null,"choices":[{"finish_reason":"stop","index":0,"message":{"content":"2 + 2 = 4 \n\nLet me know if you want to try another math problem!","role":"assistant","tool_calls":null,"function_call":null}}],"usage":{"completion_tokens":22,"prompt_tokens":21,"total_tokens":43,"completion_tokens_details":null,"prompt_tokens_details":null}}
```
