```
sip-audio-session3 -h
This script can sit idle waiting for an incoming audio session, or initiate an
outgoing audio session to a SIP address. The program will close the session
and quit when Ctrl+D is pressed.

Options:
  -h, --help            show this help message and exit
  -a NAME, --account=NAME
                        The account name to use for any outgoing traffic. If
                        not supplied, the default account will be used.
  -c CONFIG_DIRECTORY, --config-directory=CONFIG_DIRECTORY
                        The configuration directory to use. This overrides the
                        default location.
  -d PLAYBACK_DIR, --playback-dir=PLAYBACK_DIR
                        Directory with wav files to be played after calling.
                        The destination SIP address is taken from the
                        filename. After playback the call is hangup
  -p, --enable_playback
                        Enable polling playback directory for new wavs.
  -l, --log-register    Log result of registrations.
  -s, --trace-sip       Dump the raw contents of incoming and outgoing SIP
                        messages.
  -j, --trace-pjsip     Print PJSIP logging output.
  -r, --auto-record     Automatic recording of voice calls.
  -n, --trace-notifications
                        Print all notifications (disabled by default).
  --disable-ringtone    Disable ringtone.
  -g, --disable-hanguptone
                        Disable hangup tone.
  -S, --disable-sound   Disables initializing the sound card.
  --auto-answer         Interval after which to answer an incoming session
                        (disabled by default). If the option is specified but
                        the interval is not, it defaults to 0 (accept the
                        session as soon as it starts ringing).
  -u AUTO_ANSWER_URIS, --auto-answer-uris=AUTO_ANSWER_URIS
                        Optional list of SIP URIs for which auto-answer is
                        allowed
  -i EXTERNAL_ID, --external-id=EXTERNAL_ID
                        id used for call control from external application
  -v SPOOL_DIR, --spool-dir=SPOOL_DIR
                        Spool dir for call control from external applications,
                        default is /var/spool/sipclients/sessions
  -t, --enable-default-devices
                        Use default audio devices
  -m, --mute            Mute microphone
  -V, --enable-video    Enable video if camera is available
  --auto-hangup         Interval after which to hang up an established session
                        (disabled by default). If the option is specified but
                        the interval is not, it defaults to 0 (hangup the
                        session as soon as it connects).
  -b, --batch           Run the program in batch mode: reading input from the
                        console is disabled and the option --auto-answer is
                        implied. This is particularly useful when running this
                        script in a non-interactive environment.
  -f, --play-failure-code
                        Play failure code using festival.
  -D, --daemonize       Enable running this program as a deamon.
  -R, --auto-reconnect  Auto reconnect call if disconnected by remote.
```
