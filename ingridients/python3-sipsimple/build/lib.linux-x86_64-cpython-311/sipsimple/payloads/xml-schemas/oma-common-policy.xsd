<?xml version="1.0" encoding="UTF-8"?>

<!--
	XDM - Common Policy
	    version - 1.0
	    date    - 27 Jun 2008

    FILE INFORMATION

	OMA Permanent Document
	   File: OMA-SUP-XSD_xdm_commonPolicy-V1_0_2-20080627-A
	   Type: Text - Schema Description

	Public Reachable Information
	   Path: http://www.openmobilealliance.org/tech/profiles
	   Name: xdm_commonPolicy-v1_0.xsd

    NORMATIVE INFORMATION

	Information about this file can be found in the specification
	    OMA-TS-XDM_Core-V1_1
	available at http://www.openmobilealliance.org/

	Send <NAME_EMAIL>

    LEGAL DISCLAIMER

	Use of this document is subject to all of the terms and conditions
	of the Use Agreement located at
		http://www.openmobilealliance.org/UseAgreement.html

	You may use this document or any part of the document for internal
	or educational purposes only, provided you do not modify, edit or
	take out of context the information in this document in any manner.
	Information contained in this document may be used, at your sole
	risk, for any purposes.

	You may not use this document in any other manner without the prior
	written permission of the Open Mobile Alliance.  The Open Mobile
	Alliance authorizes you to copy this document, provided that you
	retain all copyright and other proprietary notices contained in the
	original materials on any copies of the materials and that you
	comply strictly with these terms.  This copyright permission does
	not constitute an endorsement of the products or services.  The
	Open Mobile Alliance assumes no responsibility for errors or
	omissions in this document.

	Each Open Mobile Alliance member has agreed to use reasonable
	endeavors to inform the Open Mobile Alliance in a timely manner of
	Essential IPR as it becomes aware that the Essential IPR is related
	to the prepared or published specification.  However, the members
	do not have an obligation to conduct IPR searches.  The declared
	Essential IPR is publicly available to members and non-members of
	the Open Mobile Alliance and may be found on the "OMA IPR
	Declarations" list at http://www.openmobilealliance.org/ipr.html.
	The Open Mobile Alliance has not conducted an independent IPR review
	of this document and the information contained herein, and makes no
	representations or warranties regarding third party IPR, including
	without limitation patents, copyrights or trade secret rights.  This
	document may contain inventions for which you must obtain licenses
	from third parties before making, using or selling the inventions.
	Defined terms above are set forth in the schedule to the Open Mobile
	Alliance Application Form.

	NO REPRESENTATIONS OR WARRANTIES (WHETHER EXPRESS OR IMPLIED) ARE
	MADE BY THE OPEN MOBILE ALLIANCE OR ANY OPEN MOBILE ALLIANCE MEMBER
	OR ITS AFFILIATES REGARDING ANY OF THE IPR'S REPRESENTED ON THE "OMA
	IPR DECLARATIONS" LIST, INCLUDING, BUT NOT LIMITED TO THE ACCURACY,
	COMPLETENESS, VALIDITY OR RELEVANCE OF THE INFORMATION OR WHETHER OR
	NOT SUCH RIGHTS ARE ESSENTIAL OR NON-ESSENTIAL.

	THE OPEN MOBILE ALLIANCE IS NOT LIABLE FOR AND HEREBY DISCLAIMS ANY
	DIRECT, INDIRECT, PUNITIVE, SPECIAL, INCIDENTAL, CONSEQUENTIAL, OR
	EXEMPLARY DAMAGES ARISING OUT OF OR IN CONNECTION WITH THE USE OF
	DOCUMENTS AND THE INFORMATION CONTAINED IN THE DOCUMENTS.

	Copyright 2008 Open Mobile Alliance Ltd.  All Rights Reserved.
	Used with the permission of the Open Mobile Alliance Ltd. under the
	terms set forth above.
-->

<xs:schema targetNamespace="urn:oma:xml:xdm:common-policy"
   xmlns="urn:oma:xml:xdm:common-policy"
   xmlns:xs="http://www.w3.org/2001/XMLSchema"
   elementFormDefault="qualified" attributeFormDefault="unqualified">

   <!-- OMA specific "conditions" child elements -->
   <xs:element name="other-identity" type="emptyType"/>

   <xs:element name="external-list">
     <xs:complexType>
       <xs:sequence>
         <xs:element name="entry" type="anchorType" minOccurs="0" maxOccurs="unbounded"/>
       </xs:sequence>
     </xs:complexType>
   </xs:element>

  <xs:element name="anonymous-request" type="emptyType"/>

  <xs:complexType name="anchorType">
    <xs:attribute name="anc" type="xs:anyURI"/>
    <xs:anyAttribute processContents="lax"/>
  </xs:complexType>

  <xs:complexType name="emptyType"/>

</xs:schema>
