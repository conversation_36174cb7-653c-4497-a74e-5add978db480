﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Label="PropertySheets">
  </ImportGroup>
  <PropertyGroup Label="UserMacros">
    <VSVer>14</VSVer>
  </PropertyGroup>
  <PropertyGroup>
    <_ProjectFileVersion>14.0.22823.1</_ProjectFileVersion>
    <OutDir Condition="'$(TargetExt)'=='.lib'">..\lib\</OutDir>
    <OutDir Condition="'$(TargetExt)'=='.exe'">..\bin\</OutDir>
    <OutDir Condition="'$(TargetExt)'=='.dll'">..\lib\</OutDir>
    <TargetName>$(ProjectName)-$(TargetCPU)-$(Platform)-vc$(VSVer)-$(Configuration)</TargetName>
    <IntDir>.\output\$(ProjectName)-$(TargetCPU)-$(PlatformName)-vc$(VSVer)-$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <BrowseInformation>true</BrowseInformation>
      <WarningLevel>Level4</WarningLevel>
      <CompileAs>Default</CompileAs>
      <CompileAsWinRT>false</CompileAsWinRT>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <SDLCheck>false</SDLCheck>
    </ClCompile>
    <Lib>
      <OutputFile>..\lib\$(ProjectName)-$(TargetCPU)-$(Platform)-vc$(VSVer)-$(Configuration).lib</OutputFile>
    </Lib>
    <Link>
      <OutputFile  Condition="'$(TargetExt)'=='.exe'">..\bin\$(ProjectName)-$(TargetCPU)-$(Platform)-vc$(VSVer)-$(Configuration).exe</OutputFile>
      <GenerateWindowsMetadata Condition="'$(TargetExt)'=='.lib'">false</GenerateWindowsMetadata>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <BuildMacro Include="VSVer">
      <Value>$(VSVer)</Value>
    </BuildMacro>
  </ItemGroup>
</Project>