#
# make-mingw.inc: Mingw specific compilation switches.
#
PALM_OS_SDK_VER := 0x06000000
PALM_OS_TARGET_HOST := TARGET_HOST_PALMOS
PALM_OS_TARGET_PLATFORM := TARGET_PLATFORM_PALMSIM_WIN32
PALM_OS_BUILD_TYPE := BUILD_TYPE_DEBUG
PALM_OS_TRACE_OUTPUT := TRACE_OUTPUT_ON
PALM_OS_CPU_TYPE := CPU_ARM

export CROSS_COMPILE := 

ifeq ($(CC_NAME),gcc)
	export CFLAGS += -mno-cygwin -fexceptions -frtti
endif

export OS_CFLAGS   := 	$(CC_DEF)PJ_PALMOS=1 \
			$(CC_DEF)__PALMOS_KERNEL__=1 \
			$(CC_DEF)__PALMOS__=$(PALM_OS_SDK_VER) \
			$(CC_DEF)BUILD_TYPE=$(PALM_OS_BUILD_TYPE) \
			$(CC_DEF)TRACE_OUTPUT=$(PALM_OS_TRACE_OUTPUT) \
			$(CC_DEF)_SUPPORTS_NAMESPACE=0 \
			$(CC_DEF)_SUPPORTS_RTTI=0 \
			$(CC_DEF)TARGET_HOST=$(PALM_OS_TRAGET_HOST) \
			$(CC_DEF)TARGET_PLATFORM=$(PALM_OS_TARGET_PLATFORM)

export OS_CXXFLAGS := 

export OS_LDFLAGS  := 

export OS_SOURCES := 

