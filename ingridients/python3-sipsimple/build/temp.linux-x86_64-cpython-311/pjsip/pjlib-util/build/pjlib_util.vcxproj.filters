﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{619e66d9-efb3-437b-81c3-03a53da5cf1e}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{09de463c-0755-42b6-b6d9-37fd1fc54a4e}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\pjlib-util\base64.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\cli.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\cli_console.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\cli_telnet.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\crc32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\dns.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\dns_dump.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\dns_server.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\errno.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\getopt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\hmac_md5.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\hmac_sha1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\http_client.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\json.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\md5.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\pcap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\resolver.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\scanner.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\scanner_cis_bitwise.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\scanner_cis_uint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\sha1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\srv_resolver.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\string.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\stun_simple.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\stun_simple_client.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\symbols.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-util\xml.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\include\pjlib-util\base64.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\cli.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\cli_console.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\cli_imp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\cli_telnet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\crc32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\dns.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\dns_server.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\errno.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\getopt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\hmac_md5.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\hmac_sha1.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\http_client.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\json.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\md5.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\pcap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\resolver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\scanner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\scanner_cis_bitwise.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\scanner_cis_uint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\sha1.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\srv_resolver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\string.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\stun_simple.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib-util\xml.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>