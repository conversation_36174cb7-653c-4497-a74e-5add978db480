#!/bin/sh
#

F="configure-bb10"

if test "$*" = "--help" -o "$*" = "-h"; then
  echo "$F [--simulator] [OPTIONS]"
  echo ""
  echo "where:"
  echo "  --simulator Optional parameter to specify that the compilation"
  echo "              target is a simulator."
  echo "  OPTIONS     Other options that will be passed directly to"
  echo "              ./configure script. Run ./configure --help"
  echo "              for more info."
  exit 0
fi

# Find simulator argument
args=""
simulator="no"
for arg in "$@"; do
    if test "$arg" = "--simulator"; then
	simulator="yes"
    else
	args="$args $arg"
    fi
done

if test "$simulator" = "yes"; then
  TARGET_ARCH="x86"
  TARGET_ARCHEND=${TARGET_ARCH}
  LIBDIR=${TARGET_ARCH}
  TARGET_HOST="i486-pc-nto-qnx8.0.0"
else
  TARGET_ARCH="armv7"
  TARGET_ARCHEND="${TARGET_ARCH}le"
  LIBDIR="armle-v7"
  TARGET_HOST="arm-unknown-nto-qnx8.0.0eabi"
fi

RANLIB="${QNX_HOST}/usr/bin/nto${TARGET_ARCH}-ranlib "
CPP="${QNX_HOST}/usr/bin/qcc -V4.6.3,gcc_nto${TARGET_ARCHEND}_cpp -E "
CC="${QNX_HOST}/usr/bin/qcc -V4.6.3,gcc_nto${TARGET_ARCHEND}_cpp "
LD="${QNX_HOST}/usr/bin/nto${TARGET_ARCH}-ld " 
export LDFLAGS="$LDFLAGS -L${QNX_TARGET}/${LIBDIR}/usr/lib -L${QNX_TARGET}/${LIBDIR}/lib -L${QNX_HOST}/usr/lib/gcc/${TARGET_HOST}/4.6.3 -lgcc -lasound -laudio_manager"

if test "$CFLAGS" = ""; then
  # Default if no CFLAGS is set in env
  export CFLAGS=" -g -O2"
fi
export CFLAGS="$CFLAGS -fPIC -DPJ_CONFIG_BB10=1 -DPJMEDIA_AUDIO_DEV_HAS_BB10=1" 

# Invoke configure
./configure --host=${TARGET_HOST} --disable-oss $args
RETVAL=$?

# Write to pjsip.pri only if configure was successful
if test $RETVAL -eq 0; then
	echo "# Config file to be included in app's .pro file" > pjsip.pri
	echo "# Auto-generated by 'configure-bb10 $*'" >> pjsip.pri
	make -f bb10-config.mak >> pjsip.pri

	echo PJSIP config file for BB10 has been written to \'pjsip.pri\'. You can include this file from your application\'s .pro file.
	echo
fi


