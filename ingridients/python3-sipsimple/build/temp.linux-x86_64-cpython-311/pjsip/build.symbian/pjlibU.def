EXPORTS
	PJ_FD_CLR                                @ 1 NONAME
	PJ_FD_COUNT                              @ 2 NONAME
	PJ_FD_ISSET                              @ 3 NONAME
	PJ_FD_SET                                @ 4 NONAME
	PJ_FD_ZERO                               @ 5 NONAME
	PJ_GUID_STRING_LENGTH                    @ 6 NONAME
	PJ_NO_MEMORY_EXCEPTION                   @ 7 NONAME
	PJ_VERSION                               @ 8 NONAME
	pj_AF_INET                               @ 9 NONAME
	pj_AF_INET6                              @ 10 NONAME
	pj_AF_IRDA                               @ 11 NONAME
	pj_AF_PACKET                             @ 12 NONAME
	pj_AF_UNIX                               @ 13 NONAME
	pj_AF_UNSPEC                             @ 14 NONAME
	pj_GUID_STRING_LENGTH                    @ 15 NONAME
	pj_IPTOS_LOWDELAY                        @ 16 NONAME
	pj_IPTOS_MINCOST                         @ 17 NONAME
	pj_IPTOS_RELIABILITY                     @ 18 NONAME
	pj_IPTOS_THROUGHPUT                      @ 19 NONAME
	pj_IP_TOS                                @ 20 NONAME
	pj_MSG_DONTROUTE                         @ 21 NONAME
	pj_MSG_OOB                               @ 22 NONAME
	pj_MSG_PEEK                              @ 23 NONAME
	pj_NO_MEMORY_EXCEPTION                   @ 24 NONAME
	pj_SOCK_DGRAM                            @ 25 NONAME
	pj_SOCK_RAW                              @ 26 NONAME
	pj_SOCK_RDM                              @ 27 NONAME
	pj_SOCK_STREAM                           @ 28 NONAME
	pj_SOL_IP                                @ 29 NONAME
	pj_SOL_IPV6                              @ 30 NONAME
	pj_SOL_SOCKET                            @ 31 NONAME
	pj_SOL_TCP                               @ 32 NONAME
	pj_SOL_UDP                               @ 33 NONAME
	pj_SO_RCVBUF                             @ 34 NONAME
	pj_SO_SNDBUF                             @ 35 NONAME
	pj_SO_TYPE                               @ 36 NONAME
	pj_ansi_to_unicode                       @ 37 NONAME
	pj_array_erase                           @ 38 NONAME
	pj_array_find                            @ 39 NONAME
	pj_array_insert                          @ 40 NONAME
	pj_atexit                                @ 41 NONAME
	pj_atomic_add                            @ 42 NONAME
	pj_atomic_add_and_get                    @ 43 NONAME
	pj_atomic_create                         @ 44 NONAME
	pj_atomic_dec                            @ 45 NONAME
	pj_atomic_dec_and_get                    @ 46 NONAME
	pj_atomic_destroy                        @ 47 NONAME
	pj_atomic_get                            @ 48 NONAME
	pj_atomic_inc                            @ 49 NONAME
	pj_atomic_inc_and_get                    @ 50 NONAME
	pj_atomic_set                            @ 51 NONAME
	pj_caching_pool_destroy                  @ 52 NONAME
	pj_caching_pool_init                     @ 53 NONAME
	pj_create_random_string                  @ 54 NONAME
	pj_create_unique_string                  @ 55 NONAME
	pj_dump_config                           @ 56 NONAME
	pj_elapsed_cycle                         @ 57 NONAME
	pj_elapsed_msec                          @ 58 NONAME
	pj_elapsed_nanosec                       @ 59 NONAME
	pj_elapsed_time                          @ 60 NONAME
	pj_elapsed_usec                          @ 61 NONAME
	pj_enter_critical_section                @ 62 NONAME
	pj_enum_ip_interface                     @ 63 NONAME
	pj_enum_ip_route                         @ 64 NONAME
	pj_exception_id_alloc                    @ 65 NONAME
	pj_exception_id_free                     @ 66 NONAME
	pj_exception_id_name                     @ 67 NONAME
	pj_fifobuf_alloc                         @ 68 NONAME
	pj_fifobuf_free                          @ 69 NONAME
	pj_fifobuf_init                          @ 70 NONAME
	pj_fifobuf_max_size                      @ 71 NONAME
	pj_fifobuf_unalloc                       @ 72 NONAME
	pj_file_close                            @ 73 NONAME
	pj_file_delete                           @ 74 NONAME
	pj_file_exists                           @ 75 NONAME
	pj_file_flush                            @ 76 NONAME
	pj_file_getpos                           @ 77 NONAME
	pj_file_getstat                          @ 78 NONAME
	pj_file_move                             @ 79 NONAME
	pj_file_open                             @ 80 NONAME
	pj_file_read                             @ 81 NONAME
	pj_file_setpos                           @ 82 NONAME
	pj_file_size                             @ 83 NONAME
	pj_file_write                            @ 84 NONAME
	pj_generate_unique_string                @ 85 NONAME
	pj_get_netos_error                       @ 86 NONAME
	pj_get_os_error                          @ 87 NONAME
	pj_get_timestamp                         @ 88 NONAME
	pj_get_timestamp_freq                    @ 89 NONAME
	pj_get_version                           @ 90 NONAME
	pj_getaddrinfo                           @ 91 NONAME
	pj_getdefaultipinterface                 @ 92 NONAME
	pj_gethostaddr                           @ 93 NONAME
	pj_gethostbyname                         @ 94 NONAME
	pj_gethostip                             @ 95 NONAME
	pj_gethostname                           @ 96 NONAME
	pj_getpid                                @ 97 NONAME
	pj_gettimeofday                          @ 98 NONAME
	pj_hash_calc                             @ 99 NONAME
	pj_hash_calc_tolower                     @ 100 NONAME
	pj_hash_count                            @ 101 NONAME
	pj_hash_create                           @ 102 NONAME
	pj_hash_first                            @ 103 NONAME
	pj_hash_get                              @ 104 NONAME
	pj_hash_next                             @ 105 NONAME
	pj_hash_set                              @ 106 NONAME
	pj_hash_set_np                           @ 107 NONAME
	pj_hash_this                             @ 108 NONAME
	pj_htonl                                 @ 109 NONAME
	pj_htons                                 @ 110 NONAME
	pj_inet_addr                             @ 111 NONAME
	pj_inet_addr2                            @ 112 NONAME
	pj_inet_aton                             @ 113 NONAME
	pj_inet_ntoa                             @ 114 NONAME
	pj_inet_ntop                             @ 115 NONAME
	pj_inet_ntop2                            @ 116 NONAME
	pj_inet_pton                             @ 117 NONAME
	pj_init                                  @ 118 NONAME
	pj_ioqueue_accept                        @ 119 NONAME
	pj_ioqueue_connect                       @ 120 NONAME
	pj_ioqueue_create                        @ 121 NONAME
	pj_ioqueue_destroy                       @ 122 NONAME
	pj_ioqueue_get_user_data                 @ 123 NONAME
	pj_ioqueue_is_pending                    @ 124 NONAME
	pj_ioqueue_name                          @ 125 NONAME
	pj_ioqueue_op_key_init                   @ 126 NONAME
	pj_ioqueue_poll                          @ 127 NONAME
	pj_ioqueue_post_completion               @ 128 NONAME
	pj_ioqueue_recv                          @ 129 NONAME
	pj_ioqueue_recvfrom                      @ 130 NONAME
	pj_ioqueue_register_sock                 @ 131 NONAME
	pj_ioqueue_register_sock2                @ 132 NONAME
	pj_ioqueue_send                          @ 133 NONAME
	pj_ioqueue_sendto                        @ 134 NONAME
	pj_ioqueue_set_lock                      @ 135 NONAME
	pj_ioqueue_set_user_data                 @ 136 NONAME
	pj_ioqueue_unregister                    @ 137 NONAME
	pj_leave_critical_section                @ 138 NONAME
	pj_list_erase                            @ 139 NONAME
	pj_list_find_node                        @ 140 NONAME
	pj_list_insert_after                     @ 141 NONAME
	pj_list_insert_before                    @ 142 NONAME
	pj_list_insert_nodes_after               @ 143 NONAME
	pj_list_insert_nodes_before              @ 144 NONAME
	pj_list_merge_first                      @ 145 NONAME
	pj_list_merge_last                       @ 146 NONAME
	pj_list_search                           @ 147 NONAME
	pj_list_size                             @ 148 NONAME
	pj_lock_acquire                          @ 149 NONAME
	pj_lock_create_null_mutex                @ 150 NONAME
	pj_lock_create_recursive_mutex           @ 151 NONAME
	pj_lock_create_semaphore                 @ 152 NONAME
	pj_lock_create_simple_mutex              @ 153 NONAME
	pj_lock_destroy                          @ 154 NONAME
	pj_lock_release                          @ 155 NONAME
	pj_lock_tryacquire                       @ 156 NONAME
	pj_log                                   @ 157 NONAME
	pj_log_1                                 @ 158 NONAME
	pj_log_2                                 @ 159 NONAME
	pj_log_3                                 @ 160 NONAME
	pj_log_4                                 @ 161 NONAME
	pj_log_5                                 @ 162 NONAME
	pj_log_get_decor                         @ 163 NONAME
	pj_log_get_level                         @ 164 NONAME
	pj_log_get_log_func                      @ 165 NONAME
	pj_log_set_decor                         @ 166 NONAME
	pj_log_set_level                         @ 167 NONAME
	pj_log_set_log_func                      @ 168 NONAME
	pj_log_write                             @ 169 NONAME
	pj_mutex_create                          @ 170 NONAME
	pj_mutex_create_recursive                @ 171 NONAME
	pj_mutex_create_simple                   @ 172 NONAME
	pj_mutex_destroy                         @ 173 NONAME
	pj_mutex_lock                            @ 174 NONAME
	pj_mutex_trylock                         @ 175 NONAME
	pj_mutex_unlock                          @ 176 NONAME
	pj_ntohl                                 @ 177 NONAME
	pj_ntohs                                 @ 178 NONAME
	pj_pool_alloc                            @ 179 NONAME
	pj_pool_alloc_from_block                 @ 180 NONAME
	pj_pool_allocate_find                    @ 181 NONAME
	pj_pool_calloc                           @ 182 NONAME
	pj_pool_create                           @ 183 NONAME
	pj_pool_create_int                       @ 184 NONAME
	pj_pool_create_on_buf                    @ 185 NONAME
	pj_pool_destroy_int                      @ 186 NONAME
	pj_pool_factory_default_policy           @ 187 NONAME
	pj_pool_factory_get_default_policy       @ 188 NONAME
	pj_pool_get_capacity                     @ 189 NONAME
	pj_pool_get_used_size                    @ 190 NONAME
	pj_pool_getobjname                       @ 191 NONAME
	pj_pool_init_int                         @ 192 NONAME
	pj_pool_release                          @ 193 NONAME
	pj_pool_reset                            @ 194 NONAME
	pj_rand                                  @ 195 NONAME
	pj_rbtree_erase                          @ 196 NONAME
	pj_rbtree_find                           @ 197 NONAME
	pj_rbtree_first                          @ 198 NONAME
	pj_rbtree_init                           @ 199 NONAME
	pj_rbtree_insert                         @ 200 NONAME
	pj_rbtree_last                           @ 201 NONAME
	pj_rbtree_max_height                     @ 202 NONAME
	pj_rbtree_min_height                     @ 203 NONAME
	pj_rbtree_next                           @ 204 NONAME
	pj_rbtree_prev                           @ 205 NONAME
	pj_register_strerror                     @ 206 NONAME
	pj_rwmutex_create                        @ 207 NONAME
	pj_rwmutex_destroy                       @ 208 NONAME
	pj_rwmutex_lock_read                     @ 209 NONAME
	pj_rwmutex_lock_write                    @ 210 NONAME
	pj_rwmutex_unlock_read                   @ 211 NONAME
	pj_rwmutex_unlock_write                  @ 212 NONAME
	pj_sem_create                            @ 213 NONAME
	pj_sem_destroy                           @ 214 NONAME
	pj_sem_post                              @ 215 NONAME
	pj_sem_trywait                           @ 216 NONAME
	pj_sem_wait                              @ 217 NONAME
	pj_set_netos_error                       @ 218 NONAME
	pj_set_os_error                          @ 219 NONAME
	pj_shutdown                              @ 220 NONAME
	pj_sock_accept                           @ 221 NONAME
	pj_sock_bind                             @ 222 NONAME
	pj_sock_bind_in                          @ 223 NONAME
	pj_sock_close                            @ 224 NONAME
	pj_sock_connect                          @ 225 NONAME
	pj_sock_getpeername                      @ 226 NONAME
	pj_sock_getsockname                      @ 227 NONAME
	pj_sock_getsockopt                       @ 228 NONAME
	pj_sock_listen                           @ 229 NONAME
	pj_sock_recv                             @ 230 NONAME
	pj_sock_recvfrom                         @ 231 NONAME
	pj_sock_select                           @ 232 NONAME
	pj_sock_send                             @ 233 NONAME
	pj_sock_sendto                           @ 234 NONAME
	pj_sock_setsockopt                       @ 235 NONAME
	pj_sock_shutdown                         @ 236 NONAME
	pj_sock_socket                           @ 237 NONAME
	pj_sockaddr_cmp                          @ 238 NONAME
	pj_sockaddr_copy_addr                    @ 239 NONAME
	pj_sockaddr_get_addr                     @ 240 NONAME
	pj_sockaddr_get_addr_len                 @ 241 NONAME
	pj_sockaddr_get_len                      @ 242 NONAME
	pj_sockaddr_get_port                     @ 243 NONAME
	pj_sockaddr_has_addr                     @ 244 NONAME
	pj_sockaddr_in_get_addr                  @ 245 NONAME
	pj_sockaddr_in_get_port                  @ 246 NONAME
	pj_sockaddr_in_init                      @ 247 NONAME
	pj_sockaddr_in_set_addr                  @ 248 NONAME
	pj_sockaddr_in_set_port                  @ 249 NONAME
	pj_sockaddr_in_set_str_addr              @ 250 NONAME
	pj_sockaddr_init                         @ 251 NONAME
	pj_sockaddr_print                        @ 252 NONAME
	pj_sockaddr_set_port                     @ 253 NONAME
	pj_sockaddr_set_str_addr                 @ 254 NONAME
	pj_srand                                 @ 255 NONAME
	pj_str                                   @ 256 NONAME
	pj_strassign                             @ 257 NONAME
	pj_strcat                                @ 258 NONAME
	pj_strcat2                               @ 259 NONAME
	pj_strcmp                                @ 260 NONAME
	pj_strcmp2                               @ 261 NONAME
	pj_strcpy                                @ 262 NONAME
	pj_strcpy2                               @ 263 NONAME
	pj_strdup                                @ 264 NONAME
	pj_strdup2                               @ 265 NONAME
	pj_strdup2_with_null                     @ 266 NONAME
	pj_strdup3                               @ 267 NONAME
	pj_strdup_with_null                      @ 268 NONAME
	pj_strerror                              @ 269 NONAME
	pj_stricmp                               @ 270 NONAME
	pj_stricmp2                              @ 271 NONAME
	pj_strltrim                              @ 272 NONAME
	pj_strncmp                               @ 273 NONAME
	pj_strncmp2                              @ 274 NONAME
	pj_strncpy                               @ 275 NONAME
	pj_strncpy_with_null                     @ 276 NONAME
	pj_strnicmp                              @ 277 NONAME
	pj_strnicmp2                             @ 278 NONAME
	pj_strrtrim                              @ 279 NONAME
	pj_strtoul                               @ 280 NONAME
	pj_strtoul2                              @ 281 NONAME
	pj_strtrim                               @ 282 NONAME
	pj_symbianos_poll                        @ 283 NONAME
	pj_symbianos_set_params                  @ 284 NONAME
	pj_thread_check_stack                    @ 285 NONAME
	pj_thread_create                         @ 286 NONAME
	pj_thread_destroy                        @ 287 NONAME
	pj_thread_get_name                       @ 288 NONAME
	pj_thread_get_os_handle                  @ 289 NONAME
	pj_thread_get_stack_info                 @ 290 NONAME
	pj_thread_get_stack_max_usage            @ 291 NONAME
	pj_thread_is_registered                  @ 292 NONAME
	pj_thread_join                           @ 293 NONAME
	pj_thread_local_alloc                    @ 294 NONAME
	pj_thread_local_free                     @ 295 NONAME
	pj_thread_local_get                      @ 296 NONAME
	pj_thread_local_set                      @ 297 NONAME
	pj_thread_register                       @ 298 NONAME
	pj_thread_resume                         @ 299 NONAME
	pj_thread_sleep                          @ 300 NONAME
	pj_thread_this                           @ 301 NONAME
	pj_time_decode                           @ 302 NONAME
	pj_time_encode                           @ 303 NONAME
	pj_time_gmt_to_local                     @ 304 NONAME
	pj_time_local_to_gmt                     @ 305 NONAME
	pj_time_val_normalize                    @ 306 NONAME
	pj_timer_entry_init                      @ 307 NONAME
	pj_timer_heap_cancel                     @ 308 NONAME
	pj_timer_heap_cancel_if_active           @ 309 NONAME
	pj_timer_heap_count                      @ 310 NONAME
	pj_timer_heap_create                     @ 311 NONAME
	pj_timer_heap_destroy                    @ 312 NONAME
	pj_timer_heap_earliest_time              @ 313 NONAME
	pj_timer_heap_mem_size                   @ 314 NONAME
	pj_timer_heap_poll                       @ 315 NONAME
	pj_timer_heap_schedule                   @ 316 NONAME
	pj_timer_heap_schedule_w_grp_lock        @ 317 NONAME
	pj_timer_heap_set_lock                   @ 318 NONAME
	pj_timer_heap_set_max_timed_out_per_poll @ 319 NONAME
	pj_unicode_to_ansi                       @ 320 NONAME
	pj_utoa                                  @ 321 NONAME
	pj_utoa_pad                              @ 322 NONAME
	platform_strerror                        @ 323 NONAME
	snprintf                                 @ 324 NONAME
	vsnprintf                                @ 325 NONAME