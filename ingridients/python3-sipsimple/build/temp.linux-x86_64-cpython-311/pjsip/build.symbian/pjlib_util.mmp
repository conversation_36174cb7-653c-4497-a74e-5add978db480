#if defined(PJ_BUILD_DLL)
TARGET		pjlib_util.dll
TARGETTYPE	dll
UID		0x0 0xA0000003

CAPABILITY	NONE
LIBRARY		pjlib.lib esock.lib insock.lib charconv.lib euser.lib estlib.lib
MACRO		PJ_DLL
MACRO		PJ_EXPORTING

DEFFILE		.\pjlib_util.def

#else

TARGET 		pjlib_util.lib
TARGETTYPE 	lib

#endif

SOURCEPATH	..\pjlib-util\src\pjlib-util

MACRO		PJ_M_I386=1
MACRO		PJ_SYMBIAN=1

// Must compile as C++, otherwise exception would not work
OPTION          CW -lang c++
OPTION          ARMCC --cpp --gnu
OPTION          GCC -x c++
OPTION          GCCE -x c++

//
// PJLIB-UTIL files
//
SOURCE	base64.c
SOURCE	cli.c
SOURCE	cli_console.c
SOURCE	cli_telnet.c
SOURCE	crc32.c
SOURCE	dns.c
SOURCE	dns_dump.c
SOURCE	dns_server.c
SOURCE	errno.c
SOURCE	getopt.c
SOURCE	hmac_md5.c
SOURCE	hmac_sha1.c
SOURCE	http_client.c
SOURCE	md5.c
SOURCE	pcap.c
SOURCE	resolver_wrap.cpp
SOURCE	scanner.c
SOURCE	sha1.c
SOURCE	srv_resolver.c
SOURCE	string.c
SOURCE	stun_simple.c
SOURCE	stun_simple_client.c
SOURCE	xml_wrap.cpp

//
// Header files
//
//DOCUMENT pjlib-util\\config.h
//DOCUMENT pjlib-util\\crc32.h
//DOCUMENT pjlib-util\\dns.h
//DOCUMENT pjlib-util\\errno.h
//DOCUMENT pjlib-util\\getopt.h
//DOCUMENT pjlib-util\\hmac_md5.h
//DOCUMENT pjlib-util\hmac_sha1.h
//DOCUMENT pjlib-util\http_client.h
//DOCUMENT pjlib-util\md5.h
//DOCUMENT pjlib-util\resolver.h
//DOCUMENT pjlib-util\scanner.h
//DOCUMENT pjlib-util\sha1.h
//DOCUMENT pjlib-util\srv_resolver.h
//DOCUMENT pjlib-util\string.h
//DOCUMENT pjlib-util\stun_simple.h
//DOCUMENT pjlib-util\types.h
//DOCUMENT pjlib-util\xml.h


SYSTEMINCLUDE	..\pjlib\include 
SYSTEMINCLUDE	..\pjlib-util\include

SYSTEMINCLUDE	\epoc32\include
SYSTEMINCLUDE	\epoc32\include\libc


