EXPORTS
	pjsua_acc_add                            @ 1 NONAME
	pjsua_acc_add_local                      @ 2 NONAME
	pjsua_acc_config_default                 @ 3 NONAME
	pjsua_acc_config_dup                     @ 4 NONAME
	pjsua_acc_create_request                 @ 5 NONAME
	pjsua_acc_create_uac_contact             @ 6 NONAME
	pjsua_acc_create_uas_contact             @ 7 NONAME
	pjsua_acc_del                            @ 8 NONAME
	pjsua_acc_enum_info                      @ 9 NONAME
	pjsua_acc_find_for_incoming              @ 10 NONAME
	pjsua_acc_find_for_outgoing              @ 11 NONAME
	pjsua_acc_get_count                      @ 12 NONAME
	pjsua_acc_get_default                    @ 13 NONAME
	pjsua_acc_get_info                       @ 14 NONAME
	pjsua_acc_is_valid                       @ 15 NONAME
	pjsua_acc_modify                         @ 16 NONAME
	pjsua_acc_set_default                    @ 17 NONAME
	pjsua_acc_set_online_status              @ 18 NONAME
	pjsua_acc_set_online_status2             @ 19 NONAME
	pjsua_acc_set_registration               @ 20 NONAME
	pjsua_acc_set_transport                  @ 21 NONAME
	pjsua_buddy_add                          @ 22 NONAME
	pjsua_buddy_config_default               @ 23 NONAME
	pjsua_buddy_del                          @ 24 NONAME
	pjsua_buddy_get_info                     @ 25 NONAME
	pjsua_buddy_is_valid                     @ 26 NONAME
	pjsua_buddy_subscribe_pres               @ 27 NONAME
	pjsua_buddy_update_pres                  @ 28 NONAME
	pjsua_call_answer                        @ 29 NONAME
	pjsua_call_dial_dtmf                     @ 30 NONAME
	pjsua_call_dump                          @ 31 NONAME
	pjsua_call_get_conf_port                 @ 32 NONAME
	pjsua_call_get_count                     @ 33 NONAME
	pjsua_call_get_info                      @ 34 NONAME
	pjsua_call_get_max_count                 @ 35 NONAME
	pjsua_call_get_rem_nat_type              @ 36 NONAME
	pjsua_call_get_user_data                 @ 37 NONAME
	pjsua_call_hangup                        @ 38 NONAME
	pjsua_call_hangup_all                    @ 39 NONAME
	pjsua_call_has_media                     @ 40 NONAME
	pjsua_call_is_active                     @ 41 NONAME
	pjsua_call_make_call                     @ 42 NONAME
	pjsua_call_reinvite                      @ 43 NONAME
	pjsua_call_send_im                       @ 44 NONAME
	pjsua_call_send_request                  @ 45 NONAME
	pjsua_call_send_typing_ind               @ 46 NONAME
	pjsua_call_set_hold                      @ 47 NONAME
	pjsua_call_set_user_data                 @ 48 NONAME
	pjsua_call_update                        @ 49 NONAME
	pjsua_call_xfer                          @ 50 NONAME
	pjsua_call_xfer_replaces                 @ 51 NONAME
	pjsua_codec_get_param                    @ 52 NONAME
	pjsua_codec_set_param                    @ 53 NONAME
	pjsua_codec_set_priority                 @ 54 NONAME
	pjsua_conf_add_port                      @ 55 NONAME
	pjsua_conf_adjust_rx_level               @ 56 NONAME
	pjsua_conf_adjust_tx_level               @ 57 NONAME
	pjsua_conf_connect                       @ 58 NONAME
	pjsua_conf_disconnect                    @ 59 NONAME
	pjsua_conf_get_active_ports              @ 60 NONAME
	pjsua_conf_get_max_ports                 @ 61 NONAME
	pjsua_conf_get_port_info                 @ 62 NONAME
	pjsua_conf_get_signal_level              @ 63 NONAME
	pjsua_conf_remove_port                   @ 64 NONAME
	pjsua_config_default                     @ 65 NONAME
	pjsua_config_dup                         @ 66 NONAME
	pjsua_create                             @ 67 NONAME
	pjsua_destroy                            @ 68 NONAME
	pjsua_detect_nat_type                    @ 69 NONAME
	pjsua_dump                               @ 70 NONAME
	pjsua_enum_accs                          @ 71 NONAME
	pjsua_enum_buddies                       @ 72 NONAME
	pjsua_enum_calls                         @ 73 NONAME
	pjsua_enum_codecs                        @ 74 NONAME
	pjsua_enum_conf_ports                    @ 75 NONAME
	pjsua_enum_snd_devs                      @ 76 NONAME
	pjsua_enum_transports                    @ 77 NONAME
	pjsua_get_buddy_count                    @ 78 NONAME
	pjsua_get_ec_tail                        @ 79 NONAME
	pjsua_get_nat_type                       @ 80 NONAME
	pjsua_get_pjmedia_endpt                  @ 81 NONAME
	pjsua_get_pjsip_endpt                    @ 82 NONAME
	pjsua_get_pool_factory                   @ 83 NONAME
	pjsua_get_snd_dev                        @ 84 NONAME
	pjsua_get_var                            @ 85 NONAME
	pjsua_handle_events                      @ 86 NONAME
	pjsua_im_send                            @ 87 NONAME
	pjsua_im_typing                          @ 88 NONAME
	pjsua_init                               @ 89 NONAME
	pjsua_logging_config_default             @ 90 NONAME
	pjsua_logging_config_dup                 @ 91 NONAME
	pjsua_media_config_default               @ 92 NONAME
	pjsua_media_transports_create            @ 93 NONAME
	pjsua_msg_data_init                      @ 94 NONAME
	pjsua_perror                             @ 95 NONAME
	pjsua_player_create                      @ 96 NONAME
	pjsua_player_destroy                     @ 97 NONAME
	pjsua_player_get_conf_port               @ 98 NONAME
	pjsua_player_get_port                    @ 99 NONAME
	pjsua_player_set_pos                     @ 100 NONAME
	pjsua_playlist_create                    @ 101 NONAME
	pjsua_pool_create                        @ 102 NONAME
	pjsua_pres_dump                          @ 103 NONAME
	pjsua_reconfigure_logging                @ 104 NONAME
	pjsua_recorder_create                    @ 105 NONAME
	pjsua_recorder_destroy                   @ 106 NONAME
	pjsua_recorder_get_conf_port             @ 107 NONAME
	pjsua_recorder_get_port                  @ 108 NONAME
	pjsua_set_ec                             @ 109 NONAME
	pjsua_set_no_snd_dev                     @ 110 NONAME
	pjsua_set_null_snd_dev                   @ 111 NONAME
	pjsua_set_snd_dev                        @ 112 NONAME
	pjsua_start                              @ 113 NONAME
	pjsua_transport_close                    @ 114 NONAME
	pjsua_transport_config_default           @ 115 NONAME
	pjsua_transport_config_dup               @ 116 NONAME
	pjsua_transport_create                   @ 117 NONAME
	pjsua_transport_get_info                 @ 118 NONAME
	pjsua_transport_register                 @ 119 NONAME
	pjsua_transport_set_enable               @ 120 NONAME
	pjsua_verify_sip_url                     @ 121 NONAME
