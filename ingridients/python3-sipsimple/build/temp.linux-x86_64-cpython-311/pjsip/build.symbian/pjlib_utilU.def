EXPORTS
	pj_cis_add_alpha                         @ 1 NONAME
	pj_cis_add_cis                           @ 2 NONAME
	pj_cis_add_num                           @ 3 NONAME
	pj_cis_add_range                         @ 4 NONAME
	pj_cis_add_str                           @ 5 NONAME
	pj_cis_buf_init                          @ 6 NONAME
	pj_cis_del_range                         @ 7 NONAME
	pj_cis_del_str                           @ 8 NONAME
	pj_cis_dup                               @ 9 NONAME
	pj_cis_init                              @ 10 NONAME
	pj_cis_invert                            @ 11 NONAME
	pj_crc32_calc                            @ 12 NONAME
	pj_crc32_final                           @ 13 NONAME
	pj_crc32_init                            @ 14 NONAME
	pj_crc32_update                          @ 15 NONAME
	pj_dns_dump_packet                       @ 16 NONAME
	pj_dns_get_type_name                     @ 17 NONAME
	pj_dns_make_query                        @ 18 NONAME
	pj_dns_packet_dup                        @ 19 NONAME
	pj_dns_parse_a_response                  @ 20 NONAME
	pj_dns_parse_packet                      @ 21 NONAME
	pj_dns_resolver_add_entry                @ 22 NONAME
	pj_dns_resolver_cancel_query             @ 23 NONAME
	pj_dns_resolver_create                   @ 24 NONAME
	pj_dns_resolver_destroy                  @ 25 NONAME
	pj_dns_resolver_dump                     @ 26 NONAME
	pj_dns_resolver_get_cached_count         @ 27 NONAME
	pj_dns_resolver_get_settings             @ 28 NONAME
	pj_dns_resolver_handle_events            @ 29 NONAME
	pj_dns_resolver_set_ns                   @ 30 NONAME
	pj_dns_resolver_set_settings             @ 31 NONAME
	pj_dns_resolver_start_query              @ 32 NONAME
	pj_dns_settings_default                  @ 33 NONAME
	pj_dns_srv_resolve                       @ 34 NONAME
	pj_hmac_md5                              @ 35 NONAME
	pj_hmac_md5_final                        @ 36 NONAME
	pj_hmac_md5_init                         @ 37 NONAME
	pj_hmac_md5_update                       @ 38 NONAME
	pj_hmac_sha1                             @ 39 NONAME
	pj_hmac_sha1_final                       @ 40 NONAME
	pj_hmac_sha1_init                        @ 41 NONAME
	pj_hmac_sha1_update                      @ 42 NONAME
	pj_md5_final                             @ 43 NONAME
	pj_md5_init                              @ 44 NONAME
	pj_md5_update                            @ 45 NONAME
	pj_scan_advance_n                        @ 46 NONAME
	pj_scan_fini                             @ 47 NONAME
	pj_scan_get                              @ 48 NONAME
	pj_scan_get_char                         @ 49 NONAME
	pj_scan_get_n                            @ 50 NONAME
	pj_scan_get_newline                      @ 51 NONAME
	pj_scan_get_quote                        @ 52 NONAME
	pj_scan_get_quotes                       @ 53 NONAME
	pj_scan_get_unescape                     @ 54 NONAME
	pj_scan_get_until                        @ 55 NONAME
	pj_scan_get_until_ch                     @ 56 NONAME
	pj_scan_get_until_chr                    @ 57 NONAME
	pj_scan_init                             @ 58 NONAME
	pj_scan_peek                             @ 59 NONAME
	pj_scan_peek_n                           @ 60 NONAME
	pj_scan_peek_until                       @ 61 NONAME
	pj_scan_restore_state                    @ 62 NONAME
	pj_scan_save_state                       @ 63 NONAME
	pj_scan_skip_line                        @ 64 NONAME
	pj_scan_skip_whitespace                  @ 65 NONAME
	pj_scan_strcmp                           @ 66 NONAME
	pj_scan_stricmp                          @ 67 NONAME
	pj_scan_stricmp_alnum                    @ 68 NONAME
	pj_sha1_final                            @ 69 NONAME
	pj_sha1_init                             @ 70 NONAME
	pj_sha1_update                           @ 71 NONAME
	pj_str_unescape                          @ 72 NONAME
	pj_strcpy_unescape                       @ 73 NONAME
	pj_strncpy2_escape                       @ 74 NONAME
	pj_strncpy_escape                        @ 75 NONAME
	pj_xml_add_attr                          @ 76 NONAME
	pj_xml_add_node                          @ 77 NONAME
	pj_xml_attr_new                          @ 78 NONAME
	pj_xml_clone                             @ 79 NONAME
	pj_xml_find                              @ 80 NONAME
	pj_xml_find_attr                         @ 81 NONAME
	pj_xml_find_next_node                    @ 82 NONAME
	pj_xml_find_node                         @ 83 NONAME
	pj_xml_node_new                          @ 84 NONAME
	pj_xml_parse                             @ 85 NONAME
	pj_xml_print                             @ 86 NONAME
	pjlib_util_init                          @ 87 NONAME
	pjstun_create_bind_req                   @ 88 NONAME
	pjstun_get_mapped_addr                   @ 89 NONAME
	pjstun_msg_find_attr                     @ 90 NONAME
	pjstun_parse_msg                         @ 91 NONAME
