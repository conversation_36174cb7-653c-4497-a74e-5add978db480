EXPORTS
	pjpidf_create                            @ 1 NONAME
	pjpidf_parse                             @ 2 NONAME
	pjpidf_pres_add_note                     @ 3 NONAME
	pjpidf_pres_add_tuple                    @ 4 NONAME
	pjpidf_pres_construct                    @ 5 NONAME
	pjpidf_pres_find_tuple                   @ 6 NONAME
	pjpidf_pres_get_first_note               @ 7 NONAME
	pjpidf_pres_get_first_tuple              @ 8 NONAME
	pjpidf_pres_get_next_note                @ 9 NONAME
	pjpidf_pres_get_next_tuple               @ 10 NONAME
	pjpidf_pres_remove_tuple                 @ 11 NONAME
	pjpidf_print                             @ 12 NONAME
	pjpidf_status_construct                  @ 13 NONAME
	pjpidf_status_is_basic_open              @ 14 NONAME
	pjpidf_status_set_basic_open             @ 15 NONAME
	pjpidf_tuple_add_note                    @ 16 NONAME
	pjpidf_tuple_construct                   @ 17 NONAME
	pjpidf_tuple_get_contact                 @ 18 NONAME
	pjpidf_tuple_get_contact_prio            @ 19 NONAME
	pjpidf_tuple_get_first_note              @ 20 NONAME
	pjpidf_tuple_get_id                      @ 21 NONAME
	pjpidf_tuple_get_next_note               @ 22 NONAME
	pjpidf_tuple_get_status                  @ 23 NONAME
	pjpidf_tuple_get_timestamp               @ 24 NONAME
	pjpidf_tuple_set_contact                 @ 25 NONAME
	pjpidf_tuple_set_contact_prio            @ 26 NONAME
	pjpidf_tuple_set_id                      @ 27 NONAME
	pjpidf_tuple_set_timestamp               @ 28 NONAME
	pjpidf_tuple_set_timestamp_np            @ 29 NONAME
	pjrpid_add_element                       @ 30 NONAME
	pjrpid_element_dup                       @ 31 NONAME
	pjrpid_get_element                       @ 32 NONAME
	pjsip_allow_events_hdr_create            @ 33 NONAME
	pjsip_event_hdr_create                   @ 34 NONAME
	pjsip_evsub_accept                       @ 35 NONAME
	pjsip_evsub_create_uac                   @ 36 NONAME
	pjsip_evsub_create_uas                   @ 37 NONAME
	pjsip_evsub_current_notify               @ 38 NONAME
	pjsip_evsub_get_allow_events_hdr         @ 39 NONAME
	pjsip_evsub_get_mod_data                 @ 40 NONAME
	pjsip_evsub_get_state                    @ 41 NONAME
	pjsip_evsub_get_state_name               @ 42 NONAME
	pjsip_evsub_init_module                  @ 43 NONAME
	pjsip_evsub_init_parser                  @ 44 NONAME
	pjsip_evsub_initiate                     @ 45 NONAME
	pjsip_evsub_instance                     @ 46 NONAME
	pjsip_evsub_notify                       @ 47 NONAME
	pjsip_evsub_register_pkg                 @ 48 NONAME
	pjsip_evsub_send_request                 @ 49 NONAME
	pjsip_evsub_set_mod_data                 @ 50 NONAME
	pjsip_evsub_terminate                    @ 51 NONAME
	pjsip_get_notify_method                  @ 52 NONAME
	pjsip_get_subscribe_method               @ 53 NONAME
	pjsip_iscomposing_create_body            @ 54 NONAME
	pjsip_iscomposing_create_xml             @ 55 NONAME
	pjsip_iscomposing_parse                  @ 56 NONAME
	pjsip_notify_method                      @ 57 NONAME
	pjsip_pres_accept                        @ 58 NONAME
	pjsip_pres_create_pidf                   @ 59 NONAME
	pjsip_pres_create_uac                    @ 60 NONAME
	pjsip_pres_create_uas                    @ 61 NONAME
	pjsip_pres_create_xpidf                  @ 62 NONAME
	pjsip_pres_current_notify                @ 63 NONAME
	pjsip_pres_get_status                    @ 64 NONAME
	pjsip_pres_init_module                   @ 65 NONAME
	pjsip_pres_initiate                      @ 66 NONAME
	pjsip_pres_instance                      @ 67 NONAME
	pjsip_pres_notify                        @ 68 NONAME
	pjsip_pres_parse_pidf                    @ 69 NONAME
	pjsip_pres_parse_xpidf                   @ 70 NONAME
	pjsip_pres_send_request                  @ 71 NONAME
	pjsip_pres_set_status                    @ 72 NONAME
	pjsip_pres_terminate                     @ 73 NONAME
	pjsip_publishc_create                    @ 74 NONAME
	pjsip_publishc_destroy                   @ 75 NONAME
	pjsip_publishc_get_pool                  @ 76 NONAME
	pjsip_publishc_init                      @ 77 NONAME
	pjsip_publishc_init_module               @ 78 NONAME
	pjsip_publishc_publish                   @ 79 NONAME
	pjsip_publishc_send                      @ 80 NONAME
	pjsip_publishc_set_credentials           @ 81 NONAME
	pjsip_publishc_set_route_set             @ 82 NONAME
	pjsip_publishc_unpublish                 @ 83 NONAME
	pjsip_publishc_update_expires            @ 84 NONAME
	pjsip_sub_state_hdr_create               @ 85 NONAME
	pjsip_subscribe_method                   @ 86 NONAME
	pjsip_tsx_get_evsub                      @ 87 NONAME
	pjsipsimple_strerror                     @ 88 NONAME
	pjxpidf_create                           @ 89 NONAME
	pjxpidf_get_status                       @ 90 NONAME
	pjxpidf_get_uri                          @ 91 NONAME
	pjxpidf_parse                            @ 92 NONAME
	pjxpidf_print                            @ 93 NONAME
	pjxpidf_set_status                       @ 94 NONAME
	pjxpidf_set_uri                          @ 95 NONAME
