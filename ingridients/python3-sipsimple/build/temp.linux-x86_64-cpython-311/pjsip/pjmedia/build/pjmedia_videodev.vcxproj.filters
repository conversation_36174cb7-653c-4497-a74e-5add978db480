﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4899336d-07ac-46f4-8746-0e791939d4e5}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{11df22b5-15be-47bf-b275-4fd246912770}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\pjmedia-videodev\avi_dev.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjmedia-videodev\colorbar_dev.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjmedia-videodev\dshow_dev.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjmedia-videodev\dshowclasses.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjmedia-videodev\errno.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjmedia-videodev\ffmpeg_dev.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjmedia-videodev\sdl_dev.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjmedia-videodev\videodev.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\include\pjmedia-videodev\avi_dev.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjmedia-videodev\config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjmedia-videodev\errno.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjmedia-videodev\videodev.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjmedia-videodev\videodev_imp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>