# Microsoft eMbedded Visual Tools Project File - Name="pjmedia_auddev_wince" - Package Owner=<4>
# Microsoft eMbedded Visual Tools Generated Build File, Format Version 6.02
# ** DO NOT EDIT **

# TARGTYPE "Win32 (WCE x86) Static Library" 0x8304
# TARGTYPE "Win32 (WCE ARMV4) Static Library" 0xa304
# TARGTYPE "Win32 (WCE ARMV4I) Static Library" 0xa504
# TARGTYPE "Win32 (WCE emulator) Static Library" 0xa604
# TARGTYPE "Win32 (WCE ARMV4T) Static Library" 0xa404

CFG=pjmedia_auddev_wince - Win32 (WCE x86) Debug
!MESSAGE This is not a valid makefile. To build this project using NMAKE,
!MESSAGE use the Export Makefile command and run
!MESSAGE 
!MESSAGE NMAKE /f "pjmedia_auddev_wince.vcn".
!MESSAGE 
!MESSAGE You can specify a configuration when running NMAKE
!MESSAGE by defining the macro CFG on the command line. For example:
!MESSAGE 
!MESSAGE NMAKE /f "pjmedia_auddev_wince.vcn" CFG="pjmedia_auddev_wince - Win32 (WCE x86) Debug"
!MESSAGE 
!MESSAGE Possible choices for configuration are:
!MESSAGE 
!MESSAGE "pjmedia_auddev_wince - Win32 (WCE emulator) Release" (based on "Win32 (WCE emulator) Static Library")
!MESSAGE "pjmedia_auddev_wince - Win32 (WCE emulator) Debug" (based on "Win32 (WCE emulator) Static Library")
!MESSAGE "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release" (based on "Win32 (WCE ARMV4I) Static Library")
!MESSAGE "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug" (based on "Win32 (WCE ARMV4I) Static Library")
!MESSAGE "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release" (based on "Win32 (WCE ARMV4) Static Library")
!MESSAGE "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug" (based on "Win32 (WCE ARMV4) Static Library")
!MESSAGE "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release" (based on "Win32 (WCE ARMV4T) Static Library")
!MESSAGE "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug" (based on "Win32 (WCE ARMV4T) Static Library")
!MESSAGE "pjmedia_auddev_wince - Win32 (WCE x86) Release" (based on "Win32 (WCE x86) Static Library")
!MESSAGE "pjmedia_auddev_wince - Win32 (WCE x86) Debug" (based on "Win32 (WCE x86) Static Library")
!MESSAGE 

# Begin Project
# PROP AllowPerConfigDependencies 0
# PROP Scc_ProjName ""
# PROP Scc_LocalPath ""
# PROP ATL_Project 2

!IF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Release"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "./output/pjmedia_audiodev_wince_emulatorRel"
# PROP BASE Intermediate_Dir "./output/pjmedia_audiodev_wince_emulatorRel"
# PROP BASE CPU_ID "{32E52003-403E-442D-BE48-DE10F8C6131D}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "./output/pjmedia_audiodev_wince_emulatorRel"
# PROP Intermediate_Dir "./output/pjmedia_audiodev_wince_emulatorRel"
# PROP CPU_ID "{32E52003-403E-442D-BE48-DE10F8C6131D}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
CPP=cl.exe
# ADD BASE CPP /nologo /W3 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D "_i386_" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_X86_" /D "x86" /D "NDEBUG" /D "_LIB" /YX /Gs8192 /GF /O2 /c
# ADD CPP /nologo /W3 /I "../../include" /I "../../../pjlib/include" /I "../../../pjlib-util/include" /I "../../../third_party/portaudio/include" /D "NDEBUG" /D "_i386_" /D "_X86_" /D "x86" /D HAVE_CONFIG_H=1 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /Gs8192 /GF /O2 /c
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Debug"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 1
# PROP BASE Output_Dir "./output/pjmedia_audiodev_wince_emulatorDbg"
# PROP BASE Intermediate_Dir "./output/pjmedia_audiodev_wince_emulatorDbg"
# PROP BASE CPU_ID "{32E52003-403E-442D-BE48-DE10F8C6131D}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 1
# PROP Output_Dir "./output/pjmedia_audiodev_wince_emulatorDbg"
# PROP Intermediate_Dir "./output/pjmedia_audiodev_wince_emulatorDbg"
# PROP CPU_ID "{32E52003-403E-442D-BE48-DE10F8C6131D}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
CPP=cl.exe
# ADD BASE CPP /nologo /W3 /Zi /Od /D "DEBUG" /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D "_i386_" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_X86_" /D "x86" /D "_LIB" /YX /Gs8192 /GF /c
# ADD CPP /nologo /W3 /Zi /Od /I "../../include" /I "../../../pjlib/include" /I "../../../pjlib-util/include" /I "../../../third_party/portaudio/include" /D "DEBUG" /D "_i386_" /D "_X86_" /D "x86" /D HAVE_CONFIG_H=1 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /Gs8192 /GF /c
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "./output/pjmedia_audiodev_wince_ARMV4IRel"
# PROP BASE Intermediate_Dir "./output/pjmedia_audiodev_wince_ARMV4IRel"
# PROP BASE CPU_ID "{DC70F430-E78B-494F-A9D5-62ADC56443B8}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "./output/pjmedia_audiodev_wince_ARMV4IRel"
# PROP Intermediate_Dir "./output/pjmedia_audiodev_wince_ARMV4IRel"
# PROP CPU_ID "{DC70F430-E78B-494F-A9D5-62ADC56443B8}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
CPP=clarm.exe
# ADD BASE CPP /nologo /W3 /D _WIN32_WCE=$(CEVersion) /D "ARM" /D "_ARM_" /D "$(CePlatform)" /D "ARMV4I" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "NDEBUG" /D "_LIB" /YX /QRarch4T /QRinterwork-return /O2 /M$(CECrtMT) /c
# ADD CPP /nologo /W3 /I "../../include" /I "../../../pjlib/include" /I "../../../pjlib-util/include" /I "../../../third_party/portaudio/include" /D "ARMV4I" /D "NDEBUG" /D "ARM" /D "_ARM_" /D HAVE_CONFIG_H=1 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /QRarch4T /QRinterwork-return /O2 /M$(CECrtMT) /c
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 1
# PROP BASE Output_Dir "./output/pjmedia_audiodev_wince_ARMV4IDbg"
# PROP BASE Intermediate_Dir "./output/pjmedia_audiodev_wince_ARMV4IDbg"
# PROP BASE CPU_ID "{DC70F430-E78B-494F-A9D5-62ADC56443B8}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 1
# PROP Output_Dir "./output/pjmedia_audiodev_wince_ARMV4IDbg"
# PROP Intermediate_Dir "./output/pjmedia_audiodev_wince_ARMV4IDbg"
# PROP CPU_ID "{DC70F430-E78B-494F-A9D5-62ADC56443B8}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
CPP=clarm.exe
# ADD BASE CPP /nologo /W3 /Zi /Od /D "DEBUG" /D _WIN32_WCE=$(CEVersion) /D "ARM" /D "_ARM_" /D "$(CePlatform)" /D "ARMV4I" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /QRarch4T /QRinterwork-return /M$(CECrtMTDebug) /c
# ADD CPP /nologo /W3 /Zi /Od /I "../../include" /I "../../../pjlib/include" /I "../../../pjlib-util/include" /I "../../../third_party/portaudio/include" /D "DEBUG" /D "ARMV4I" /D "ARM" /D "_ARM_" /D HAVE_CONFIG_H=1 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /QRarch4T /QRinterwork-return /M$(CECrtMTDebug) /c
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "./output/pjmedia_audiodev_wince_ARMV4Rel"
# PROP BASE Intermediate_Dir "./output/pjmedia_audiodev_wince_ARMV4Rel"
# PROP BASE CPU_ID "{ECBEA43D-CD7B-4852-AD55-D4227B5D624B}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "./output/pjmedia_audiodev_wince_ARMV4Rel"
# PROP Intermediate_Dir "./output/pjmedia_audiodev_wince_ARMV4Rel"
# PROP CPU_ID "{ECBEA43D-CD7B-4852-AD55-D4227B5D624B}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
CPP=clarm.exe
# ADD BASE CPP /nologo /W3 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D "NDEBUG" /D "ARM" /D "_ARM_" /D "ARMV4" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /O2 /M$(CECrtMT) /c
# ADD CPP /nologo /W3 /I "../../include" /I "../../../pjlib/include" /I "../../../pjlib-util/include" /I "../../../third_party/portaudio/include" /D "NDEBUG" /D "ARMV4" /D "ARM" /D "_ARM_" /D HAVE_CONFIG_H=1 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /O2 /M$(CECrtMT) /c
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 1
# PROP BASE Output_Dir "./output/pjmedia_audiodev_wince_ARMV4Dbg"
# PROP BASE Intermediate_Dir "./output/pjmedia_audiodev_wince_ARMV4Dbg"
# PROP BASE CPU_ID "{ECBEA43D-CD7B-4852-AD55-D4227B5D624B}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 1
# PROP Output_Dir "./output/pjmedia_auddev_ARMV4Dbg"
# PROP Intermediate_Dir "./output/pjmedia_auddev_ARMV4Dbg"
# PROP CPU_ID "{ECBEA43D-CD7B-4852-AD55-D4227B5D624B}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
CPP=clarm.exe
# ADD BASE CPP /nologo /W3 /Zi /Od /D "DEBUG" /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D "ARM" /D "_ARM_" /D "ARMV4" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /M$(CECrtMTDebug) /c
# ADD CPP /nologo /W3 /Zi /Od /I "../../include" /I "../../../pjlib/include" /I "../../../pjlib-util/include" /I "../../../third_party/portaudio/include" /D "DEBUG" /D "ARMV4" /D "ARM" /D "_ARM_" /D HAVE_CONFIG_H=1 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /M$(CECrtMTDebug) /c
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "./output/pjmedia_audiodev_wince_ARMV4TRel"
# PROP BASE Intermediate_Dir "./output/pjmedia_audiodev_wince_ARMV4TRel"
# PROP BASE CPU_ID "{F52316A9-3B7C-4FE7-A67F-68350B41240D}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "./output/pjmedia_audiodev_wince_ARMV4TRel"
# PROP Intermediate_Dir "./output/pjmedia_audiodev_wince_ARMV4TRel"
# PROP CPU_ID "{F52316A9-3B7C-4FE7-A67F-68350B41240D}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
CPP=clthumb.exe
# ADD BASE CPP /nologo /W3 /D _WIN32_WCE=$(CEVersion) /D "ARM" /D "_ARM_" /D "$(CePlatform)" /D "THUMB" /D "_THUMB_" /D "ARMV4T" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "NDEBUG" /D "_LIB" /YX /QRarch4T /QRinterwork-return /O2 /M$(CECrtMT) /c
# ADD CPP /nologo /W3 /I "../../include" /I "../../../pjlib/include" /I "../../../pjlib-util/include" /I "../../../third_party/portaudio/include" /D "THUMB" /D "_THUMB_" /D "ARMV4T" /D "NDEBUG" /D "ARM" /D "_ARM_" /D HAVE_CONFIG_H=1 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /QRarch4T /QRinterwork-return /O2 /M$(CECrtMT) /c
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 1
# PROP BASE Output_Dir "./output/pjmedia_audiodev_wince_ARMV4TDbg"
# PROP BASE Intermediate_Dir "./output/pjmedia_audiodev_wince_ARMV4TDbg"
# PROP BASE CPU_ID "{F52316A9-3B7C-4FE7-A67F-68350B41240D}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 1
# PROP Output_Dir "./output/pjmedia_audiodev_wince_ARMV4TDbg"
# PROP Intermediate_Dir "./output/pjmedia_audiodev_wince_ARMV4TDbg"
# PROP CPU_ID "{F52316A9-3B7C-4FE7-A67F-68350B41240D}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
CPP=clthumb.exe
# ADD BASE CPP /nologo /W3 /Zi /Od /D "DEBUG" /D _WIN32_WCE=$(CEVersion) /D "ARM" /D "_ARM_" /D "$(CePlatform)" /D "THUMB" /D "_THUMB_" /D "ARMV4T" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /QRarch4T /QRinterwork-return /M$(CECrtMTDebug) /c
# ADD CPP /nologo /W3 /Zi /Od /I "../../include" /I "../../../pjlib/include" /I "../../../pjlib-util/include" /I "../../../third_party/portaudio/include" /D "DEBUG" /D "THUMB" /D "_THUMB_" /D "ARMV4T" /D "ARM" /D "_ARM_" /D HAVE_CONFIG_H=1 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /QRarch4T /QRinterwork-return /M$(CECrtMTDebug) /c
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Release"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "./output/pjmedia_audiodev_wince_X86Rel"
# PROP BASE Intermediate_Dir "./output/pjmedia_audiodev_wince_X86Rel"
# PROP BASE CPU_ID "{D6518FF3-710F-11D3-99F2-00105A0DF099}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "./output/pjmedia_audiodev_wince_X86Rel"
# PROP Intermediate_Dir "./output/pjmedia_audiodev_wince_X86Rel"
# PROP CPU_ID "{D6518FF3-710F-11D3-99F2-00105A0DF099}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
CPP=cl.exe
# ADD BASE CPP /nologo /W3 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D "_i386_" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_X86_" /D "x86" /D "NDEBUG" /D "_LIB" /YX /Gs8192 /GF /O2 /c
# ADD CPP /nologo /W3 /I "../../include" /I "../../../pjlib/include" /I "../../../pjlib-util/include" /I "../../../third_party/portaudio/include" /D "NDEBUG" /D "_i386_" /D "_X86_" /D "x86" /D HAVE_CONFIG_H=1 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /Gs8192 /GF /O2 /c
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Debug"

# PROP BASE Use_MFC 0
# PROP BASE Use_Debug_Libraries 1
# PROP BASE Output_Dir "./output/pjmedia_audiodev_wince_X86Dbg"
# PROP BASE Intermediate_Dir "./output/pjmedia_audiodev_wince_X86Dbg"
# PROP BASE CPU_ID "{D6518FF3-710F-11D3-99F2-00105A0DF099}"
# PROP BASE Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP BASE Target_Dir ""
# PROP Use_MFC 0
# PROP Use_Debug_Libraries 1
# PROP Output_Dir "./output/pjmedia_audiodev_wince_X86Dbg"
# PROP Intermediate_Dir "./output/pjmedia_audiodev_wince_X86Dbg"
# PROP CPU_ID "{D6518FF3-710F-11D3-99F2-00105A0DF099}"
# PROP Platform_ID "{8A9A2F80-6887-11D3-842E-005004848CBA}"
# PROP Target_Dir ""
CPP=cl.exe
# ADD BASE CPP /nologo /W3 /Zi /Od /D "DEBUG" /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D "_i386_" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_X86_" /D "x86" /D "_LIB" /YX /Gs8192 /GF /c
# ADD CPP /nologo /W3 /Zi /Od /I "../../include" /I "../../../pjlib/include" /I "../../../pjlib-util/include" /I "../../../third_party/portaudio/include" /D "DEBUG" /D "_i386_" /D "_X86_" /D "x86" /D HAVE_CONFIG_H=1 /D _WIN32_WCE=$(CEVersion) /D "$(CePlatform)" /D UNDER_CE=$(CEVersion) /D "UNICODE" /D "_UNICODE" /D "_LIB" /YX /Gs8192 /GF /c
LIB32=link.exe -lib
# ADD BASE LIB32 /nologo
# ADD LIB32 /nologo
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo

!ENDIF 

# Begin Target

# Name "pjmedia_auddev_wince - Win32 (WCE emulator) Release"
# Name "pjmedia_auddev_wince - Win32 (WCE emulator) Debug"
# Name "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release"
# Name "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug"
# Name "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release"
# Name "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug"
# Name "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release"
# Name "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug"
# Name "pjmedia_auddev_wince - Win32 (WCE x86) Release"
# Name "pjmedia_auddev_wince - Win32 (WCE x86) Debug"
# Begin Group "Source Files"

# PROP Default_Filter "cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
# Begin Source File

SOURCE="..\..\src\pjmedia-audiodev\audiodev.c"

!IF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Release"

DEP_CPP_AUDIO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Debug"

DEP_CPP_AUDIO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release"

DEP_CPP_AUDIO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug"

DEP_CPP_AUDIO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release"

DEP_CPP_AUDIO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug"

DEP_CPP_AUDIO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release"

DEP_CPP_AUDIO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug"

DEP_CPP_AUDIO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Release"

DEP_CPP_AUDIO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Debug"

DEP_CPP_AUDIO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ENDIF 

# End Source File
# Begin Source File

SOURCE="..\..\src\pjmedia-audiodev\audiotest.c"

!IF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Release"

DEP_CPP_AUDIOT=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Debug"

DEP_CPP_AUDIOT=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release"

DEP_CPP_AUDIOT=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug"

DEP_CPP_AUDIOT=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release"

DEP_CPP_AUDIOT=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug"

# PROP Exclude_From_Build 1

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release"

DEP_CPP_AUDIOT=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug"

DEP_CPP_AUDIOT=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Release"

DEP_CPP_AUDIOT=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Debug"

DEP_CPP_AUDIOT=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ENDIF 

# End Source File
# Begin Source File

SOURCE="..\..\src\pjmedia-audiodev\errno.c"

!IF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Release"

DEP_CPP_ERRNO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\..\third_party\portaudio\include\portaudio.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Debug"

DEP_CPP_ERRNO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\..\third_party\portaudio\include\portaudio.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release"

DEP_CPP_ERRNO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug"

DEP_CPP_ERRNO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release"

DEP_CPP_ERRNO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\..\third_party\portaudio\include\portaudio.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug"

DEP_CPP_ERRNO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release"

DEP_CPP_ERRNO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug"

DEP_CPP_ERRNO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Release"

DEP_CPP_ERRNO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Debug"

DEP_CPP_ERRNO=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ENDIF 

# End Source File
# Begin Source File

SOURCE="..\..\src\pjmedia-audiodev\legacy_dev.c"

!IF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Release"

DEP_CPP_LEGAC=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\sound.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Debug"

DEP_CPP_LEGAC=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\sound.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release"

DEP_CPP_LEGAC=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\sound.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug"

DEP_CPP_LEGAC=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\sound.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release"

DEP_CPP_LEGAC=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\sound.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug"

DEP_CPP_LEGAC=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\sound.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release"

DEP_CPP_LEGAC=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\sound.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug"

DEP_CPP_LEGAC=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\sound.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Release"

DEP_CPP_LEGAC=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\sound.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Debug"

DEP_CPP_LEGAC=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\sound.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ENDIF 

# End Source File
# Begin Source File

SOURCE="..\..\src\pjmedia-audiodev\pa_dev.c"

!IF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Release"

DEP_CPP_PA_DE=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\..\third_party\portaudio\include\portaudio.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Debug"

DEP_CPP_PA_DE=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\..\third_party\portaudio\include\portaudio.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release"

DEP_CPP_PA_DE=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug"

DEP_CPP_PA_DE=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release"

DEP_CPP_PA_DE=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\..\third_party\portaudio\include\portaudio.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug"

DEP_CPP_PA_DE=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release"

DEP_CPP_PA_DE=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug"

DEP_CPP_PA_DE=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Release"

DEP_CPP_PA_DE=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Debug"

DEP_CPP_PA_DE=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ENDIF 

# End Source File
# Begin Source File

SOURCE="..\..\src\pjmedia-audiodev\symb_aps_dev.cpp"

!IF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Release"

DEP_CPP_SYMB_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia-codec\amr_helper.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	"..\..\src\pjmedia-audiodev\s60_g729_bitstream.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Debug"

DEP_CPP_SYMB_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia-codec\amr_helper.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	"..\..\src\pjmedia-audiodev\s60_g729_bitstream.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release"

DEP_CPP_SYMB_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia-codec\amr_helper.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	"..\..\src\pjmedia-audiodev\s60_g729_bitstream.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug"

DEP_CPP_SYMB_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia-codec\amr_helper.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	"..\..\src\pjmedia-audiodev\s60_g729_bitstream.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release"

DEP_CPP_SYMB_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia-codec\amr_helper.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	"..\..\src\pjmedia-audiodev\s60_g729_bitstream.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug"

# PROP Exclude_From_Build 1

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release"

DEP_CPP_SYMB_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia-codec\amr_helper.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	"..\..\src\pjmedia-audiodev\s60_g729_bitstream.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug"

DEP_CPP_SYMB_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia-codec\amr_helper.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	"..\..\src\pjmedia-audiodev\s60_g729_bitstream.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Release"

DEP_CPP_SYMB_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia-codec\amr_helper.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	"..\..\src\pjmedia-audiodev\s60_g729_bitstream.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Debug"

DEP_CPP_SYMB_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia-codec\amr_helper.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	"..\..\src\pjmedia-audiodev\s60_g729_bitstream.h"\
	

!ENDIF 

# End Source File
# Begin Source File

SOURCE="..\..\src\pjmedia-audiodev\symb_mda_dev.cpp"

!IF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Release"

DEP_CPP_SYMB_M=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Debug"

DEP_CPP_SYMB_M=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release"

DEP_CPP_SYMB_M=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug"

DEP_CPP_SYMB_M=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release"

DEP_CPP_SYMB_M=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug"

# PROP Exclude_From_Build 1

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release"

DEP_CPP_SYMB_M=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug"

DEP_CPP_SYMB_M=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Release"

DEP_CPP_SYMB_M=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Debug"

DEP_CPP_SYMB_M=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\alaw_ulaw.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ENDIF 

# End Source File
# Begin Source File

SOURCE="..\..\src\pjmedia-audiodev\wmme_dev.c"

!IF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Release"

DEP_CPP_WMME_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Debug"

DEP_CPP_WMME_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release"

DEP_CPP_WMME_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug"

DEP_CPP_WMME_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release"

DEP_CPP_WMME_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug"

DEP_CPP_WMME_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release"

DEP_CPP_WMME_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug"

DEP_CPP_WMME_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Release"

DEP_CPP_WMME_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Debug"

DEP_CPP_WMME_=\
	"..\..\..\pjlib\include\pj\activesock.h"\
	"..\..\..\pjlib\include\pj\addr_resolv.h"\
	"..\..\..\pjlib\include\pj\array.h"\
	"..\..\..\pjlib\include\pj\assert.h"\
	"..\..\..\pjlib\include\pj\compat\assert.h"\
	"..\..\..\pjlib\include\pj\compat\cc_armcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_codew.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_gcce.h"\
	"..\..\..\pjlib\include\pj\compat\cc_msvc.h"\
	"..\..\..\pjlib\include\pj\compat\cc_mwcc.h"\
	"..\..\..\pjlib\include\pj\compat\ctype.h"\
	"..\..\..\pjlib\include\pj\compat\errno.h"\
	"..\..\..\pjlib\include\pj\compat\high_precision.h"\
	"..\..\..\pjlib\include\pj\compat\m_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_auto.h"\
	"..\..\..\pjlib\include\pj\compat\os_darwinos.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux.h"\
	"..\..\..\pjlib\include\pj\compat\os_linux_kernel.h"\
	"..\..\..\pjlib\include\pj\compat\os_palmos.h"\
	"..\..\..\pjlib\include\pj\compat\os_rtems.h"\
	"..\..\..\pjlib\include\pj\compat\os_sunos.h"\
	"..\..\..\pjlib\include\pj\compat\os_symbian.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32.h"\
	"..\..\..\pjlib\include\pj\compat\os_win32_wince.h"\
	"..\..\..\pjlib\include\pj\compat\setjmp.h"\
	"..\..\..\pjlib\include\pj\compat\size_t.h"\
	"..\..\..\pjlib\include\pj\compat\stdarg.h"\
	"..\..\..\pjlib\include\pj\compat\string.h"\
	"..\..\..\pjlib\include\pj\config.h"\
	"..\..\..\pjlib\include\pj\config_site.h"\
	"..\..\..\pjlib\include\pj\config_site_sample.h"\
	"..\..\..\pjlib\include\pj\ctype.h"\
	"..\..\..\pjlib\include\pj\errno.h"\
	"..\..\..\pjlib\include\pj\except.h"\
	"..\..\..\pjlib\include\pj\fifobuf.h"\
	"..\..\..\pjlib\include\pj\file_access.h"\
	"..\..\..\pjlib\include\pj\file_io.h"\
	"..\..\..\pjlib\include\pj\guid.h"\
	"..\..\..\pjlib\include\pj\hash.h"\
	"..\..\..\pjlib\include\pj\ioqueue.h"\
	"..\..\..\pjlib\include\pj\ip_helper.h"\
	"..\..\..\pjlib\include\pj\list.h"\
	"..\..\..\pjlib\include\pj\list_i.h"\
	"..\..\..\pjlib\include\pj\lock.h"\
	"..\..\..\pjlib\include\pj\log.h"\
	"..\..\..\pjlib\include\pj\math.h"\
	"..\..\..\pjlib\include\pj\os.h"\
	"..\..\..\pjlib\include\pj\pool.h"\
	"..\..\..\pjlib\include\pj\pool_alt.h"\
	"..\..\..\pjlib\include\pj\pool_buf.h"\
	"..\..\..\pjlib\include\pj\pool_i.h"\
	"..\..\..\pjlib\include\pj\rand.h"\
	"..\..\..\pjlib\include\pj\rbtree.h"\
	"..\..\..\pjlib\include\pj\sock.h"\
	"..\..\..\pjlib\include\pj\sock_select.h"\
	"..\..\..\pjlib\include\pj\string.h"\
	"..\..\..\pjlib\include\pj\string_i.h"\
	"..\..\..\pjlib\include\pj\timer.h"\
	"..\..\..\pjlib\include\pj\types.h"\
	"..\..\..\pjlib\include\pj\unicode.h"\
	"..\..\..\pjlib\include\pjlib.h"\
	"..\..\include\pjmedia-audiodev\audiodev.h"\
	"..\..\include\pjmedia-audiodev\audiodev_imp.h"\
	"..\..\include\pjmedia-audiodev\audiotest.h"\
	"..\..\include\pjmedia-audiodev\config.h"\
	"..\..\include\pjmedia-audiodev\errno.h"\
	"..\..\include\pjmedia\config.h"\
	"..\..\include\pjmedia\config_auto.h"\
	"..\..\include\pjmedia\types.h"\
	"..\..\include\pjmedia_audiodev.h"\
	

!ENDIF 

# End Source File
# End Group
# Begin Group "Header Files"

# PROP Default_Filter "h;hpp;hxx;hm;inl"
# Begin Source File

SOURCE="..\..\include\pjmedia-audiodev\audiodev.h"
# End Source File
# Begin Source File

SOURCE="..\..\include\pjmedia-audiodev\audiodev_imp.h"
# End Source File
# Begin Source File

SOURCE="..\..\include\pjmedia-audiodev\audiotest.h"

!IF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Release"

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE emulator) Debug"

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Release"

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4I) Debug"

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Release"

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4) Debug"

# PROP Exclude_From_Build 1

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Release"

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE ARMV4T) Debug"

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Release"

!ELSEIF  "$(CFG)" == "pjmedia_auddev_wince - Win32 (WCE x86) Debug"

!ENDIF 

# End Source File
# Begin Source File

SOURCE="..\..\include\pjmedia-audiodev\config.h"
# End Source File
# Begin Source File

SOURCE="..\..\include\pjmedia-audiodev\errno.h"
# End Source File
# End Group
# End Target
# End Project
