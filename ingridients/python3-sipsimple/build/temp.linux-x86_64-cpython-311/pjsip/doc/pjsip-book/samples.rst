
PJSUA2 Sample Applications
***********************************

Sample Apps
===========

C++
-----
There is a very simple C++ sample application available in ``pjsip-apps/src/samples/pjsua2_demo.cpp``. The binary will be located in ``pjsip-apps/bin/samples``.


Python GUI
------------------
This is a rather complete Python GUI sample apps, located in ``pjsip-apps/src/pygui``. It requires Python 2.7 and above, and the Python SWIG module of course. To use the application, simply run::

    python application.py

Android
----------------
Please see https://trac.pjsip.org/repos/wiki/Getting-Started/Android#pjsua2 for Android sample application.

Java
----------------
There is a Hello World type of application located in ``pjsip-apps/src/swig/java``. This requires the Java SWIG module. After building the SWIG module, run ``make test`` from this directory to run the app.


Miscellaneous
===================

How to 
-----------------------------

