
Introduction
*******************************
This documentation is intended for developers looking to develop Session Initiation Protocol (SIP) based client application. Some knowledge on SIP is definitely required, and of course some programming experience. Prior knowledge of PJSUA C API is not needed, although it will probably help.

PJSIP libraries provide multi-level APIs to do SIP calls, presence, and instant messaging, as well as handling media and NAT traversal. PJSUA2 API is the highest API from PJSIP, on top of PJSUA-LIB API. PJSUA-LIB API itself is a library that unifies SIP, audio/video media, NAT traversal, and client media application best practices into a high level, integrated, and easy to use API. The next chapter will guide you on selecting which API level to use depending on your requirements.

This documentation can be `viewed online <http://www.pjsip.org/docs/book-latest/html/index.html>`_, or alternatively you can `download the PDF format <http://www.pjsip.org/docs/book-latest/PJSUA2Doc.pdf>`_ for offline viewing.

Getting Started with PJSIP
==============================
Check `PJSIP Datasheet <http://trac.pjsip.org/repos/wiki/PJSIP-Datasheet>`_ to make sure that it has the features that you require.

To start using PJSIP, the `Getting Started Guide <http://trac.pjsip.org/repos/wiki/Getting-Started>`_ contains instructions to acquire and build PJSIP on various platforms that we support.


PJSIP Info and Documentation
================================
To get other relevant info and documentations about PJSIP, you can visit:

- `PJSIP General Wiki <http://trac.pjsip.org/repos/wiki>`_ is the home for all documentation
- `PJSIP FAQ <http://trac.pjsip.org/repos/wiki/FAQ>`_
- `PJSIP Reference Manual <http://trac.pjsip.org/repos/wiki>`_ - please see Reference Manual section



