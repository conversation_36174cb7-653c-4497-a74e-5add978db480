﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{c8a8e8d9-1fbe-477c-91c9-5f4c495a2ad1}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Source Files\Other Targets">
      <UniqueIdentifier>{366d56a2-30a3-409b-9bd6-14ddc94069e0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{77df8652-bc15-4733-a454-bbf8f16f93f1}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Header Files\compat">
      <UniqueIdentifier>{3e7b3f2e-8699-43c9-801e-2c54e3f07023}</UniqueIdentifier>
    </Filter>
    <Filter Include="Inline Files">
      <UniqueIdentifier>{4befc994-9aa4-47c8-99e7-5a51301220dd}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\winrt">
      <UniqueIdentifier>{4a92fce6-2308-44cf-aa66-00ccd5b333bd}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\winrt">
      <UniqueIdentifier>{3550aa38-c59d-4d5f-b458-1f93e0b16bbd}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\pj\activesock.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\addr_resolv_sock.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\array.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\config.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ctype.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\errno.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\except.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\fifobuf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\file_access_win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\file_io_ansi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\file_io_win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\guid.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\guid_win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\hash.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ioqueue_common_abs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ioqueue_select.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ioqueue_winnt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ip_helper_win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\list.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\lock.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\log.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\log_writer_stdout.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\os_core_win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\os_error_win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\os_info.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\os_time_win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\os_timestamp_common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\os_timestamp_win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\pool.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\pool_buf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\pool_caching.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\pool_dbg.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\pool_policy_malloc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\rand.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\rbtree.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\sock_bsd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\sock_common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\sock_qos_bsd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\sock_qos_common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\sock_qos_dummy.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\sock_qos_wm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\sock_select.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ssl_sock_common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ssl_sock_dump.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ssl_sock_ossl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\string.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\timer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\types.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\unicode_win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\addr_resolv_linux_kernel.c">
      <Filter>Source Files\Other Targets</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\guid_simple.c">
      <Filter>Source Files\Other Targets</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ioqueue_dummy.c">
      <Filter>Source Files\Other Targets</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ioqueue_epoll.c">
      <Filter>Source Files\Other Targets</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\log_writer_printk.c">
      <Filter>Source Files\Other Targets</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\os_core_unix.c">
      <Filter>Source Files\Other Targets</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\os_error_unix.c">
      <Filter>Source Files\Other Targets</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\os_timestamp_linux.c">
      <Filter>Source Files\Other Targets</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\pool_policy_kmalloc.c">
      <Filter>Source Files\Other Targets</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\symbols.c">
      <Filter>Source Files\Other Targets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third_party\threademulation\src\ThreadEmulation.cpp">
      <Filter>Source Files\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ip_helper_winphone8.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ip_helper_generic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ip_helper_generic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ssl_sock_gtls.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pj\ssl_sock_imp_common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\src\pj\ioqueue_common_abs.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\activesock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\addr_resolv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\array.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\assert.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\config_site.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\config_site_sample.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\ctype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\doxygen.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\errno.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\except.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\fifobuf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\file_access.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\file_io.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\guid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\hash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\ioqueue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\ip_helper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\list.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\lock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\log.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\math.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\os.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pjlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\pool.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\pool_alt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\pool_buf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\rand.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\rbtree.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\sock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\sock_qos.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\sock_select.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\ssl_sock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\string.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\timer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\unicode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\assert.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\cc_gcc.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\cc_msvc.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\ctype.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\errno.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\high_precision.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\m_alpha.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\m_i386.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\m_m68k.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\m_sparc.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\malloc.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\os_linux.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\os_palmos.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\os_sunos.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\os_win32.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\os_win32_wince.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\rand.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\setjmp.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\size_t.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\socket.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\stdarg.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\stdfileio.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\string.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\time.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\list_i.h">
      <Filter>Inline Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\pool_i.h">
      <Filter>Inline Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\string_i.h">
      <Filter>Inline Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\os_winphone8.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third_party\threademulation\include\ThreadEmulation.h">
      <Filter>Header Files\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\os_winuwp.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\limits.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pj\compat\limits.h">
      <Filter>Header Files\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\src\pj\ssl_sock_imp_common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>