﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{87ba28ea-fd15-43b2-8da2-52ca1fedaa86}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{d65a57ff-143e-46bc-92e3-bef39475b72a}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{131d925a-9a14-4c13-87f9-4350e5b57a65}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\pjlib-test\activesock.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\atomic.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\echo_clt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\errno.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\exception.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\fifobuf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\file.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\hash_test.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\ioq_perf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\ioq_tcp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\ioq_udp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\ioq_unreg.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\list.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\main_mod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\main_win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\mutex.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\os.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\pool.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\pool_perf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\rand.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\rbtree.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\select.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\sleep.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\sock.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\sock_perf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\ssl_sock.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\string.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\test.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\thread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\timer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\timestamp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\udp_echo_srv_ioqueue.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\udp_echo_srv_sync.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pjlib-test\util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\src\pjlib-test\test.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>