<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="pjlib"
	ProjectGUID="{DA0E03ED-53A7-4050-8A85-90541C5509F8}"
	RootNamespace="pjlib"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
		<Platform
			Name="x64"
		/>
		<Platform
			Name="Pocket PC 2003 (ARMV4)"
		/>
		<Platform
			Name="Smartphone 2003 (ARMV4)"
		/>
		<Platform
			Name="Windows Mobile 6 Standard SDK (ARMV4I)"
		/>
		<Platform
			Name="Windows Mobile 6 Professional SDK (ARMV4I)"
		/>
		<Platform
			Name="Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
		/>
		<Platform
			Name="Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-win32-common-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|x64"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-win64-common-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-win32-release-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-win64-release-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug-Static|Win32"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-win32-common-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug-Static|x64"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-win64-common-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release-Dynamic|Win32"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-win32-release-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release-Dynamic|x64"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-win64-release-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug-Dynamic|Win32"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-win32-common-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug-Dynamic|x64"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-win64-common-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release-Static|Win32"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-win32-release-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release-Static|x64"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-win64-release-defaults.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|Pocket PC 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Smartphone 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Pocket PC 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Smartphone 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Static|Pocket PC 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Static|Smartphone 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Static|Pocket PC 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Static|Smartphone 2003 (ARMV4)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm2003-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm2003sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6std-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6pro-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6std-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6pro-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6std-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6pro-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6std-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6pro-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6std-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6pro-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6std-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm6-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm6pro-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-debug-dynamic-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-common-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5ppc-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
			ConfigurationType="4"
			InheritedPropertySheets="..\..\build\vs\pjproject-vs8-release-static-defaults.vsprops;..\..\build\vs\pjproject-vs8-wm5-release-defaults.vsprops"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				AdditionalIncludeDirectories="../include"
				PreprocessorDefinitions="_LIB;"
				PrecompiledHeaderFile=""
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="..\lib\$(ProjectName)-$(TargetCPU)-wm5sp-vc$(VSVer)-$(ConfigurationName).lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="..\src\pj\activesock.c"
				>
			</File>
			<File
				RelativePath="..\src\pj\addr_resolv_sock.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\array.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\config.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\ctype.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\errno.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\except.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\fifobuf.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\file_access_win32.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\file_io_ansi.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\file_io_win32.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\guid.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\guid_win32.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\hash.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\ioqueue_common_abs.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\ioqueue_common_abs.h"
				>
			</File>
			<File
				RelativePath="..\src\pj\ioqueue_select.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\ioqueue_winnt.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Pocket PC 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Smartphone 2003 (ARMV4)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\ip_helper_win32.c"
				>
			</File>
			<File
				RelativePath="..\src\pj\list.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\lock.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\log.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\log_writer_stdout.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\os_core_win32.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\os_error_win32.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\os_info.c"
				>
			</File>
			<File
				RelativePath="..\src\pj\os_time_win32.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\os_timestamp_common.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\os_timestamp_win32.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\pool.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\pool_buf.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\pool_caching.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\pool_dbg.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\pool_policy_malloc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\rand.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\rbtree.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\sock_bsd.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\sock_common.c"
				>
			</File>
			<File
				RelativePath="..\src\pj\sock_qos_bsd.c"
				>
			</File>
			<File
				RelativePath="..\src\pj\sock_qos_common.c"
				>
			</File>
			<File
				RelativePath="..\src\pj\sock_qos_dummy.c"
				>
			</File>
			<File
				RelativePath="..\src\pj\sock_qos_wm.c"
				>
			</File>
			<File
				RelativePath="..\src\pj\sock_select.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\ssl_sock_common.c"
				>
			</File>
			<File
				RelativePath="..\src\pj\ssl_sock_dump.c"
				>
			</File>
			<File
				RelativePath="..\src\pj\ssl_sock_imp_common.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					ExcludedFromBuild="true"
					>
					<Tool
						Name="VCCLCompilerTool"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\ssl_sock_imp_common.h"
				>
			</File>
			<File
				RelativePath="..\src\pj\ssl_sock_ossl.c"
				>
			</File>
			<File
				RelativePath="..\src\pj\string.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\timer.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\types.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\src\pj\unicode_win32.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug-Dynamic|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release-Static|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<Filter
				Name="Other Targets"
				>
				<File
					RelativePath="..\src\pj\guid_simple.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\src\pj\ioqueue_dummy.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\src\pj\ioqueue_epoll.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\src\pj\ip_helper_generic.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\src\pj\log_writer_printk.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\src\pj\os_core_unix.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\src\pj\os_error_unix.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\src\pj\os_timestamp_linux.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\src\pj\pool_policy_kmalloc.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\src\pj\symbols.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|x64"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Pocket PC 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Smartphone 2003 (ARMV4)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Standard SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 6 Professional SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug-Dynamic|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release-Static|Windows Mobile 5.0 Smartphone SDK (ARMV4I)"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="..\include\pj\activesock.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\addr_resolv.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\array.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\assert.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\config.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\config_site.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\config_site_sample.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\ctype.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\doxygen.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\equeue.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\errno.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\except.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\fifobuf.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\file_access.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\file_io.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\guid.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\hash.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\ioqueue.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\ip_helper.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\limits.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\list.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\lock.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\log.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\math.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\os.h"
				>
			</File>
			<File
				RelativePath="..\include\pjlib.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\pool.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\pool_alt.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\pool_buf.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\rand.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\rbtree.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\sock.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\sock_qos.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\sock_select.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\ssl_sock.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\string.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\timer.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\types.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\unicode.h"
				>
			</File>
			<Filter
				Name="compat"
				>
				<File
					RelativePath="..\include\pj\compat\assert.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\cc_gcc.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\cc_msvc.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\ctype.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\errno.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\high_precision.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\limits.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\m_alpha.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\m_i386.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\m_m68k.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\m_sparc.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\malloc.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\os_linux.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\os_palmos.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\os_sunos.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\os_win32.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\os_win32_wince.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\rand.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\setjmp.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\size_t.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\socket.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\stdarg.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\stdfileio.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\string.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\time.h"
					>
				</File>
				<File
					RelativePath="..\include\pj\compat\vsprintf.h"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Inline Files"
			>
			<File
				RelativePath="..\include\pj\list_i.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\pool_i.h"
				>
			</File>
			<File
				RelativePath="..\include\pj\string_i.h"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
