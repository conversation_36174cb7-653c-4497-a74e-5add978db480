BODY,H1,H2,H3,H4,H5,H6,P,<PERSON><PERSON><PERSON>,TD,TH,UL,DL,DIV {
	font-family: Geneva, Arial, Helvetica, sans-serif;
}
BODY,TD {
       font-size: 80%;
}
CODE {
	font-size: 120%;
       font-family: monospace;
}
.fragment, pre {
	font-size: 110%;
       font-family: monospace;
}
H1 {
	text-align: center;
       font-size: 240%;
}
H2 {
       	font-size: 200%;
	margin-top     : 60px;
}
H3 {
       font-size: 160%;
}
H4 {
       font-size: 120%;
}
CAPTION { font-weight: bold }
DIV.qindex {
	width: 100%;
	background-color: #eeeeff;
	border: 1px solid #b0b0b0;
	text-align: center;
	margin: 2px;
	padding: 2px;
	line-height: 140%;
}
DIV.nav {
	width: 100%;
	background-color: #eeeeff;
	border: 1px solid #b0b0b0;
	text-align: center;
	margin: 2px;
	padding: 2px;
	line-height: 140%;
}
A.qindex {
       text-decoration: none;
       font-size: 120%;
       color: #1A419D;
}
A.qindex:visited {
       text-decoration: none;
       color: #1A419D
}
A.qindex:hover {
	text-decoration: none;
	background-color: #ddddff;
}
A.qindexHL {
	text-decoration: none;
	font-weight: bold;
	background-color: #6666cc;
	color: #ffffff;
	border: 1px double #9295C2;
}
A.qindexHL:hover {
	text-decoration: none;
	background-color: #6666cc;
	color: #ffffff;
}
A.qindexHL:visited { text-decoration: none; background-color: #6666cc; color: #ffffff }
A.el { text-decoration: none; font-weight: bold }
A.elRef { font-weight: bold }
A.code:link { text-decoration: none; font-weight: normal; color: #0000FF; }
A.code:visited { text-decoration: none; font-weight: normal; color: #0000FF}
A.codeRef:link { font-weight: normal; color: #0000FF}
A.codeRef:visited { font-weight: normal; color: #0000FF}
A:hover { text-decoration: none; background-color: #f2f2ff }
DL.el { margin-left: -1cm }
PRE.fragment {
	border: 1px solid #CCCCCC;
	background-color: #f5f5f5;
	margin-top: 4px;
	margin-bottom: 4px;
	margin-left: 2px;
	margin-right: 8px;
	padding-left: 6px;
	padding-right: 6px;
	padding-top: 4px;
	padding-bottom: 4px;
}
DIV.ah { background-color: black; font-weight: bold; color: #ffffff; margin-bottom: 3px; margin-top: 3px }
TD.md { background-color: #F4F4FB; font-weight: bold; }
TD.mdPrefix {
       background-color: #F4F4FB;
       color: #606060;
	font-size: 80%;
}
TD.mdname1 { background-color: #F4F4FB; font-weight: bold; color: #602020; }
TD.mdname { background-color: #F4F4FB; font-weight: bold; color: #602020; width: 600px; }
DIV.groupHeader {
       margin-left: 16px;
       margin-top: 12px;
       margin-bottom: 6px;
       font-weight: bold;
}
DIV.groupText { margin-left: 16px; font-style: italic; font-size: 90% }
BODY {
	background: white;
	color: black;
	margin-right: 20px;
	margin-left: 20px;
}
TD.indexkey {
	background-color: #eeeeff;
	font-weight: bold;
	padding-right  : 10px;
	padding-top    : 2px;
	padding-left   : 10px;
	padding-bottom : 2px;
	margin-left    : 0px;
	margin-right   : 0px;
	margin-top     : 2px;
	margin-bottom  : 2px;
	border: 1px solid #CCCCCC;
}
TD.indexvalue {
	background-color: #eeeeff;
	font-style: italic;
	padding-right  : 10px;
	padding-top    : 2px;
	padding-left   : 10px;
	padding-bottom : 2px;
	margin-left    : 0px;
	margin-right   : 0px;
	margin-top     : 2px;
	margin-bottom  : 2px;
	border: 1px solid #CCCCCC;
}
TR.memlist {
   background-color: #f0f0f0; 
}
P.formulaDsp { text-align: center; }
IMG.formulaDsp { }
IMG.formulaInl { vertical-align: middle; }
SPAN.keyword       { color: #008000 }
SPAN.keywordtype   { color: #604020 }
SPAN.keywordflow   { color: #e08000 }
SPAN.comment       { color: #800000 }
SPAN.preprocessor  { color: #806020 }
SPAN.stringliteral { color: #002080 }
SPAN.charliteral   { color: #008080 }
.mdTable {
	border: 1px solid #868686;
	background-color: #F4F4FB;
}
.mdRow {
	padding: 8px 10px;
}
.mdescLeft {
       padding: 0px 8px 4px 8px;
	font-size: 80%;
	font-style: italic;
	background-color: #FAFAFA;
	border-top: 1px none #E0E0E0;
	border-right: 1px none #E0E0E0;
	border-bottom: 1px none #E0E0E0;
	border-left: 1px none #E0E0E0;
	margin: 0px;
}
.mdescRight {
       padding: 0px 8px 4px 8px;
	font-size: 80%;
	font-style: italic;
	background-color: #FAFAFA;
	border-top: 1px none #E0E0E0;
	border-right: 1px none #E0E0E0;
	border-bottom: 1px none #E0E0E0;
	border-left: 1px none #E0E0E0;
	margin: 0px;
}
.memItemLeft {
	padding: 1px 0px 0px 8px;
	margin: 4px;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-color: #E0E0E0;
	border-right-color: #E0E0E0;
	border-bottom-color: #E0E0E0;
	border-left-color: #E0E0E0;
	border-top-style: solid;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	background-color: #FAFAFA;
	font-size: 80%;
}
.memItemRight {
	padding: 1px 8px 0px 8px;
	margin: 4px;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-color: #E0E0E0;
	border-right-color: #E0E0E0;
	border-bottom-color: #E0E0E0;
	border-left-color: #E0E0E0;
	border-top-style: solid;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	background-color: #FAFAFA;
	font-size: 80%;
}
.memTemplItemLeft {
	padding: 1px 0px 0px 8px;
	margin: 4px;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-color: #E0E0E0;
	border-right-color: #E0E0E0;
	border-bottom-color: #E0E0E0;
	border-left-color: #E0E0E0;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	background-color: #FAFAFA;
	font-size: 80%;
}
.memTemplItemRight {
	padding: 1px 8px 0px 8px;
	margin: 4px;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-color: #E0E0E0;
	border-right-color: #E0E0E0;
	border-bottom-color: #E0E0E0;
	border-left-color: #E0E0E0;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	background-color: #FAFAFA;
	font-size: 80%;
}
.memTemplParams {
	padding: 1px 0px 0px 8px;
	margin: 4px;
	border-top-width: 1px;
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-top-color: #E0E0E0;
	border-right-color: #E0E0E0;
	border-bottom-color: #E0E0E0;
	border-left-color: #E0E0E0;
	border-top-style: solid;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
       color: #606060;
	background-color: #FAFAFA;
	font-size: 80%;
}
.search     { color: #003399;
              font-weight: bold;
}
FORM.search {
              margin-bottom: 0px;
              margin-top: 0px;
}
INPUT.search { font-size: 75%;
               color: #000080;
               font-weight: normal;
               background-color: #eeeeff;
}
TD.tiny      { font-size: 75%;
}
a {
	color: #252E78;
}
a:visited {
	color: #3D2185;
}
.dirtab { padding: 4px;
          border-collapse: collapse;
          border: 1px solid #b0b0b0;
}
TH.dirtab { background: #eeeeff;
            font-weight: bold;
}
HR { height: 1px;
     border: none;
     border-top: 1px solid black;
}
