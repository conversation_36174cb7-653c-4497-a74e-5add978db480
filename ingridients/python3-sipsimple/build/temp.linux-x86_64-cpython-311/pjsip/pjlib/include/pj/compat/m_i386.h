/* $Id$ */
/* 
 * Copyright (C) 2008-2011 Teluu Inc. (http://www.teluu.com)
 * Copyright (C) 2003-2008 <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA 
 */
#ifndef __PJ_COMPAT_M_i386_H__
#define __PJ_COMPAT_M_i386_H__

/**
 * @file m_i386.h
 * @brief Describes Intel i386 family processor specifics.
 */

#define PJ_M_NAME		"i386"

#define PJ_HAS_PENTIUM		1
#define PJ_IS_LITTLE_ENDIAN	1
#define PJ_IS_BIG_ENDIAN	0


#endif	/* __PJ_COMPAT_M_i386_H__ */
