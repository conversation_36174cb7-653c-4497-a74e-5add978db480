#!/usr/bin/env python3
"""
AI SIP Call Agent Backend - Starts the AI service and web interface.

USAGE:
1. Run this script: python ai_backend.py
2. In another terminal, run: python ingridients/sip-audio-session3.py
3. The AI service will be available for integration, and the SIP client will register properly.

This approach keeps the working SIP client separate while providing the AI backend services.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Import our components
from src.call_agent.config import get_config
from src.call_agent.agent import CallAgentService
from src.web_interface.app import create_web_app


async def main():
    """Main application entry point."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger('AI-SIP-Agent')
    
    print("🚀 Starting AI SIP Call Agent Backend")
    print("=" * 50)
    
    try:
        # Load configuration
        config = get_config()
        
        # Start call agent service
        print("🤖 Starting AI Call Agent Service...")
        call_agent = CallAgentService(config)
        await call_agent.start()
        print("✓ AI Call Agent Service started")
        
        # Start web interface
        print("🌐 Starting Web Interface...")
        web_app = create_web_app(call_agent)
        
        import uvicorn
        web_config = uvicorn.Config(
            web_app,
            host=config.web.host,
            port=config.web.port,
            log_level="info"
        )
        
        server = uvicorn.Server(web_config)
        web_task = asyncio.create_task(server.serve())
        
        print(f"✓ Web interface available at http://{config.web.host}:{config.web.port}")
        print()
        print("🎉 AI SIP Call Agent Backend is now running!")
        print()
        print("📋 Services Status:")
        print(f"   • AI Call Agent: ✓ Running")
        print(f"   • Web Dashboard: ✓ http://{config.web.host}:{config.web.port}")
        print(f"   • Whisper STT: {config.services.whisper.base_url}")
        print(f"   • Kokoro TTS: {config.services.kokoro_tts.base_url}")
        print(f"   • LiteLLM: {config.services.litellm.base_url}")
        print()
        print("📞 To complete the setup:")
        print("   1. Open a new terminal")
        print("   2. Run: python ingridients/sip-audio-session3.py")
        print("   3. The SIP client will register and handle calls")
        print("   4. Calls will be processed through the AI backend")
        print()
        print("🛑 Press Ctrl+C to stop the AI backend")
        
        # Keep running until interrupted
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Shutting down AI backend...")
            
        # Cleanup
        server.should_exit = True
        await web_task
        await call_agent.stop()
        
        print("✓ AI backend shutdown complete")
        
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
