#!/usr/bin/env python3
"""
Simple AI-integrated SIP client that runs the original SIP client with AI hooks.
This approach avoids inheritance issues by using a simpler integration method.
"""

import sys
import os
import subprocess
import threading
import time
import urllib.request
import urllib.parse
import json
from pathlib import Path

# Add our source to path
sys.path.insert(0, str(Path(__file__).parent))


class AICallManager:
    """Manages AI integration for SIP calls."""
    
    def __init__(self, ai_backend_url="http://localhost:8000"):
        self.ai_backend_url = ai_backend_url
        self.active_calls = {}
        self.monitoring = True
        
    def start_ai_call(self, caller_id):
        """Start an AI call session."""
        try:
            data = json.dumps({"caller_id": caller_id}).encode('utf-8')
            req = urllib.request.Request(
                f"{self.ai_backend_url}/api/calls/start",
                data=data,
                headers={'Content-Type': 'application/json'}
            )
            
            with urllib.request.urlopen(req, timeout=5) as response:
                if response.status == 200:
                    response_data = json.loads(response.read().decode('utf-8'))
                    call_id = response_data.get("call_id")
                    print(f"🤖 AI: Started call session {call_id}")
                    return call_id
                else:
                    print(f"🤖 AI: Backend error: {response.status}")
                    return None
        except Exception as e:
            print(f"🤖 AI: Connection error: {e}")
            return None
            
    def end_ai_call(self, call_id):
        """End an AI call session."""
        try:
            req = urllib.request.Request(
                f"{self.ai_backend_url}/api/calls/{call_id}/end",
                method='POST'
            )
            
            with urllib.request.urlopen(req, timeout=5) as response:
                if response.status == 200:
                    print(f"🤖 AI: Ended call session {call_id}")
                    return True
                else:
                    print(f"🤖 AI: Error ending call: {response.status}")
                    return False
        except Exception as e:
            print(f"🤖 AI: Error ending call: {e}")
            return False
            
    def monitor_sip_logs(self, sip_config_dir):
        """Monitor SIP session logs for call events."""
        sessions_dir = Path(sip_config_dir) / "spool" / "sesssions"
        
        print(f"🔍 Monitoring SIP sessions in: {sessions_dir}")
        
        processed_sessions = set()
        
        while self.monitoring:
            try:
                if sessions_dir.exists():
                    # Look for new session directories
                    for session_dir in sessions_dir.iterdir():
                        if session_dir.is_dir() and session_dir.name not in processed_sessions:
                            # New session detected
                            session_id = session_dir.name
                            processed_sessions.add(session_id)
                            
                            # Try to extract caller info from session directory name or files
                            caller_id = self._extract_caller_info(session_dir)
                            if caller_id:
                                print(f"📞 Detected new call from: {caller_id}")
                                
                                # Start AI session
                                ai_call_id = self.start_ai_call(caller_id)
                                if ai_call_id:
                                    self.active_calls[session_id] = ai_call_id
                                    
                                    # Monitor for session end
                                    threading.Thread(
                                        target=self._monitor_session_end,
                                        args=(session_dir, ai_call_id),
                                        daemon=True
                                    ).start()
                
                time.sleep(1)  # Check every second
                
            except Exception as e:
                print(f"🤖 AI: Monitoring error: {e}")
                time.sleep(5)
                
    def _extract_caller_info(self, session_dir):
        """Extract caller information from session directory."""
        try:
            # The session directory name often contains call info
            session_name = session_dir.name
            
            # Look for files that might contain caller info
            for file_path in session_dir.glob("*"):
                if file_path.is_file():
                    try:
                        content = file_path.read_text()
                        # Look for SIP URI patterns
                        if "@" in content and "sip:" in content:
                            # Extract first SIP URI found
                            lines = content.split('\n')
                            for line in lines:
                                if "sip:" in line and "@" in line:
                                    # Simple extraction - could be improved
                                    start = line.find("sip:")
                                    if start != -1:
                                        end = line.find(" ", start)
                                        if end == -1:
                                            end = line.find(">", start)
                                        if end == -1:
                                            end = len(line)
                                        return line[start:end].strip()
                    except:
                        continue
                        
            # Fallback: use session directory name
            return f"unknown@{session_name[:8]}"
            
        except Exception as e:
            print(f"🤖 AI: Error extracting caller info: {e}")
            return None
            
    def _monitor_session_end(self, session_dir, ai_call_id):
        """Monitor for session end."""
        stop_file = session_dir / "stop"
        
        # Wait for stop file or directory deletion
        while session_dir.exists() and not stop_file.exists():
            time.sleep(1)
            
        # Session ended
        print(f"📞 Call session ended")
        self.end_ai_call(ai_call_id)
        
        # Clean up
        session_id = session_dir.name
        if session_id in self.active_calls:
            del self.active_calls[session_id]


def main():
    """Main entry point."""
    print("🚀 Starting Simple AI-integrated SIP Client")
    print("=" * 50)
    
    # Check if AI backend is running
    ai_manager = AICallManager()
    try:
        req = urllib.request.Request(f"{ai_manager.ai_backend_url}/api/health")
        with urllib.request.urlopen(req, timeout=5) as response:
            if response.status == 200:
                print("✅ AI Backend is running")
            else:
                print("❌ AI Backend health check failed")
                return
    except Exception as e:
        print(f"❌ AI Backend not reachable: {e}")
        print("Make sure to run: python ai_backend.py")
        return
        
    print("🤖 This integration will:")
    print("   • Run the original SIP client")
    print("   • Monitor for incoming calls")
    print("   • Create AI sessions for each call")
    print("   • Track calls in the web dashboard")
    print()
    
    # Get SIP config directory
    sip_config_dir = os.path.expanduser('~/.sipclient')
    
    # Start monitoring in background
    monitor_thread = threading.Thread(
        target=ai_manager.monitor_sip_logs,
        args=(sip_config_dir,),
        daemon=True
    )
    monitor_thread.start()
    
    # Start the original SIP client
    sip_client_path = Path(__file__).parent / "ingridients" / "sip-audio-session3.py"
    
    print(f"📞 Starting SIP client: {sip_client_path}")
    print("🔍 Monitoring for calls...")
    print()
    
    try:
        # Run the original SIP client
        process = subprocess.Popen([
            sys.executable, str(sip_client_path)
        ], cwd=str(Path(__file__).parent))
        
        # Wait for the SIP client to finish
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        ai_manager.monitoring = False
        
        # Terminate SIP client if still running
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
            
    except Exception as e:
        print(f"❌ Error running SIP client: {e}")
        
    finally:
        ai_manager.monitoring = False
        print("👋 Goodbye!")


if __name__ == "__main__":
    main()
