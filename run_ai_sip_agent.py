#!/usr/bin/env python3
"""
Simple runner for the AI SIP Call Agent that uses the working SIP client.
This script ensures proper SIP registration and AI integration.
"""

import os
import sys
import asyncio
import logging
import threading
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Import our components
from src.call_agent.config import get_config
from src.call_agent.agent import CallAgentService
from src.web_interface.app import create_web_app

# Import the working SIP client
sys.path.insert(0, str(Path(__file__).parent / "ingridients"))

try:
    from sip_audio_session3 import SIPAudioSession
    print("✓ Successfully imported working SIP client")
except ImportError as e:
    print(f"✗ Could not import working SIP client: {e}")
    print("Make sure sip-audio-session3.py is in the ingridients directory")
    sys.exit(1)


class AIIntegratedSIPSession(SIPAudioSession):
    """SIP session with AI integration."""
    
    def __init__(self, call_agent_service):
        super().__init__()
        self.call_agent = call_agent_service
        self.active_calls = {}
        self.logger = logging.getLogger('AI-SIP')
        
    def _NH_SIPSessionNewIncoming(self, notification):
        """Handle incoming calls with AI integration."""
        session = notification.sender
        
        # Get caller information
        caller_id = str(session.remote_identity.uri)
        if session.remote_identity.display_name:
            caller_id = f"{session.remote_identity.display_name} <{caller_id}>"
            
        self.logger.info(f"🤖 AI: Incoming call from: {caller_id}")
        
        # Start AI call session
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            call_id = loop.run_until_complete(
                self.call_agent.start_call(caller_id)
            )
            
            self.active_calls[id(session)] = call_id
            self.logger.info(f"🤖 AI: Started call session {call_id}")
            
            loop.close()
            
        except Exception as e:
            self.logger.error(f"🤖 AI: Failed to start call session: {e}")
            
        # Call the original handler to accept the call
        super()._NH_SIPSessionNewIncoming(notification)
        
    def _NH_SIPSessionDidEnd(self, notification):
        """Handle call end with AI cleanup."""
        session = notification.sender
        session_id = id(session)
        
        if session_id in self.active_calls:
            call_id = self.active_calls[session_id]
            
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                loop.run_until_complete(
                    self.call_agent.end_call(call_id, "session_ended")
                )
                
                loop.close()
                self.logger.info(f"🤖 AI: Ended call session {call_id}")
                
            except Exception as e:
                self.logger.error(f"🤖 AI: Failed to end call session: {e}")
                
            del self.active_calls[session_id]
            
        # Call the original handler
        super()._NH_SIPSessionDidEnd(notification)
        
    def _NH_AudioStreamDidReceiveRTP(self, notification):
        """Handle incoming audio with AI processing."""
        # For now, just call the parent handler
        # TODO: Implement real-time audio processing
        super()._NH_AudioStreamDidReceiveRTP(notification)


async def start_call_agent():
    """Start the call agent service."""
    config = get_config()
    call_agent = CallAgentService(config)
    await call_agent.start()
    return call_agent


async def start_web_interface(call_agent):
    """Start the web interface."""
    config = get_config()
    web_app = create_web_app(call_agent)
    
    import uvicorn
    web_config = uvicorn.Config(
        web_app,
        host=config.web.host,
        port=config.web.port,
        log_level="info"
    )
    
    server = uvicorn.Server(web_config)
    return server


def run_sip_client(call_agent):
    """Run the SIP client in a separate thread."""
    logger = logging.getLogger('SIP-Client')
    
    try:
        # Create AI-integrated SIP session
        sip_session = AIIntegratedSIPSession(call_agent)
        
        # Use the same config directory as the working client
        config_directory = os.path.expanduser('~/.sipclient')
        
        logger.info("🔌 Starting SIP client with AI integration...")
        logger.info(f"📁 Using config directory: {config_directory}")
        
        # This will start the SIP client and register with the server
        sip_session.run(config_directory)
        
    except Exception as e:
        logger.error(f"❌ SIP client error: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Main application entry point."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger('Main')
    
    print("🚀 Starting AI SIP Call Agent")
    print("=" * 50)
    
    try:
        # Start call agent service
        print("🤖 Starting AI Call Agent Service...")
        call_agent = await start_call_agent()
        print("✓ AI Call Agent Service started")
        
        # Start web interface
        print("🌐 Starting Web Interface...")
        web_server = await start_web_interface(call_agent)
        web_task = asyncio.create_task(web_server.serve())
        
        config = get_config()
        print(f"✓ Web interface available at http://{config.web.host}:{config.web.port}")
        
        # Start SIP client in a separate thread
        print("📞 Starting SIP Client...")
        sip_thread = threading.Thread(
            target=run_sip_client, 
            args=(call_agent,), 
            daemon=True
        )
        sip_thread.start()
        
        # Give SIP client time to start and register
        await asyncio.sleep(3)
        
        print("✓ SIP Client started")
        print()
        print("🎉 AI SIP Call Agent is now running!")
        print("📋 Status:")
        print(f"   • SIP Client: Should be registered with your provider")
        print(f"   • AI Service: Processing calls with goal-oriented conversations")
        print(f"   • Web Dashboard: http://{config.web.host}:{config.web.port}")
        print()
        print("📞 The system will now:")
        print("   1. Automatically answer incoming SIP calls")
        print("   2. Process speech using Whisper STT")
        print("   3. Generate responses using LiteLLM")
        print("   4. Convert responses to speech using Kokoro TTS")
        print("   5. Track conversation goals and progress")
        print()
        print("🛑 Press Ctrl+C to stop")
        
        # Keep running until interrupted
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Shutting down...")
            
        # Cleanup
        web_server.should_exit = True
        await web_task
        await call_agent.stop()
        
        print("✓ Shutdown complete")
        
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
