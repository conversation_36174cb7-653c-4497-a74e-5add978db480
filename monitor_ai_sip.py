#!/usr/bin/env python3
"""
AI SIP Monitor - Monitors SIP client output and creates AI sessions for calls.
This is the simplest integration approach that avoids all inheritance issues.
"""

import sys
import os
import subprocess
import threading
import time
import urllib.request
import json
import re
from pathlib import Path


class SIPCallMonitor:
    """Monitors SIP client output for call events."""
    
    def __init__(self, ai_backend_url="http://localhost:8000"):
        self.ai_backend_url = ai_backend_url
        self.active_calls = {}
        
    def start_ai_call(self, caller_id):
        """Start an AI call session."""
        try:
            data = json.dumps({"caller_id": caller_id}).encode('utf-8')
            req = urllib.request.Request(
                f"{self.ai_backend_url}/api/calls/start",
                data=data,
                headers={'Content-Type': 'application/json'}
            )
            
            with urllib.request.urlopen(req, timeout=5) as response:
                if response.status == 200:
                    response_data = json.loads(response.read().decode('utf-8'))
                    call_id = response_data.get("call_id")
                    print(f"🤖 AI: Started call session {call_id} for {caller_id}")
                    return call_id
                else:
                    print(f"🤖 AI: Backend error: {response.status}")
                    return None
        except Exception as e:
            print(f"🤖 AI: Connection error: {e}")
            return None
            
    def end_ai_call(self, call_id):
        """End an AI call session."""
        try:
            req = urllib.request.Request(
                f"{self.ai_backend_url}/api/calls/{call_id}/end",
                method='POST'
            )
            
            with urllib.request.urlopen(req, timeout=5) as response:
                if response.status == 200:
                    print(f"🤖 AI: Ended call session {call_id}")
                    return True
        except Exception as e:
            print(f"🤖 AI: Error ending call: {e}")
            return False
            
    def parse_sip_output(self, line):
        """Parse SIP client output for call events."""
        line = line.strip()
        
        # Look for incoming call pattern
        incoming_pattern = r"Incoming audio session from ['\"]([^'\"]+)['\"]"
        match = re.search(incoming_pattern, line)
        if match:
            caller_info = match.group(1)
            print(f"📞 Detected incoming call: {caller_info}")
            
            # Extract just the SIP URI if possible
            sip_match = re.search(r'<(sip:[^>]+)>', caller_info)
            if sip_match:
                caller_id = sip_match.group(1)
            else:
                caller_id = caller_info
                
            # Start AI session
            call_id = self.start_ai_call(caller_id)
            if call_id:
                self.active_calls[caller_id] = call_id
            return
            
        # Look for session end patterns
        if "Session ended" in line or "Session cancelled" in line:
            print(f"📞 Call ended")
            # End all active AI calls (simple approach)
            for caller_id, call_id in list(self.active_calls.items()):
                self.end_ai_call(call_id)
                del self.active_calls[caller_id]
            return
            
        # Look for session start
        if "Session started" in line:
            print(f"📞 Call session established")
            return


def main():
    """Main entry point."""
    print("🚀 AI SIP Call Monitor")
    print("=" * 50)
    
    # Check if AI backend is running
    monitor = SIPCallMonitor()
    try:
        req = urllib.request.Request(f"{monitor.ai_backend_url}/api/health")
        with urllib.request.urlopen(req, timeout=5) as response:
            if response.status == 200:
                print("✅ AI Backend is running")
            else:
                print("❌ AI Backend health check failed")
                return
    except Exception as e:
        print(f"❌ AI Backend not reachable: {e}")
        print("Make sure to run: python ai_backend.py")
        return
        
    print("🤖 This monitor will:")
    print("   • Run the original SIP client")
    print("   • Monitor console output for call events")
    print("   • Create AI sessions for incoming calls")
    print("   • Track calls in the web dashboard")
    print()
    
    # Start the original SIP client with output monitoring
    sip_client_path = Path(__file__).parent / "ingridients" / "sip-audio-session3.py"
    
    print(f"📞 Starting SIP client: {sip_client_path}")
    print("🔍 Monitoring console output...")
    print("=" * 50)
    
    try:
        # Run the SIP client and capture output
        process = subprocess.Popen([
            sys.executable, str(sip_client_path)
        ], 
        stdout=subprocess.PIPE, 
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        bufsize=1,
        cwd=str(Path(__file__).parent)
        )
        
        # Monitor output in real-time
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # Print the original output
                print(output.strip())
                
                # Parse for call events
                monitor.parse_sip_output(output)
                
        # Wait for process to complete
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        
        # End all active AI calls
        for caller_id, call_id in list(monitor.active_calls.items()):
            monitor.end_ai_call(call_id)
            
        # Terminate SIP client
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            try:
                process.kill()
            except:
                pass
                
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        print("👋 Monitor stopped!")


if __name__ == "__main__":
    main()
