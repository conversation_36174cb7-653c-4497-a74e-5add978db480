#!/usr/bin/env python3
"""
AI-integrated SIP client that extends the working sip-audio-session3.py
This version communicates with the AI backend via HTTP API calls.
"""

import sys
import os
import asyncio
import aiohttp
import json
import threading
import time
from pathlib import Path

# Import the working SIP client
sys.path.insert(0, str(Path(__file__).parent / "ingridients"))
from sip_audio_session3 import SIPAudioApplication

# Add our source to path
sys.path.insert(0, str(Path(__file__).parent))


class AISIPAudioApplication(SIPAudioApplication):
    """AI-integrated SIP audio application."""
    
    def __init__(self):
        super().__init__()
        self.ai_backend_url = "http://localhost:8000"
        self.active_ai_calls = {}  # session_id -> call_id mapping
        self.audio_buffers = {}    # session_id -> audio buffer
        
    def _NH_SIPSessionNewIncoming(self, notification):
        """Handle incoming SIP session with AI integration."""
        session = notification.sender
        
        # Get caller information
        caller_id = str(session.remote_identity.uri)
        if session.remote_identity.display_name:
            caller_id = f"{session.remote_identity.display_name} <{caller_id}>"
            
        self.output.put(f"🤖 AI: Incoming call from: {caller_id}\n")
        
        # Start AI call session
        try:
            call_id = self._start_ai_call(caller_id)
            if call_id:
                session_id = id(session)
                self.active_ai_calls[session_id] = call_id
                self.audio_buffers[session_id] = bytearray()
                self.output.put(f"🤖 AI: Started call session {call_id}\n")
            else:
                self.output.put("🤖 AI: Failed to start call session\n")
        except Exception as e:
            self.output.put(f"🤖 AI: Error starting call session: {e}\n")
            
        # Call the original handler
        super()._NH_SIPSessionNewIncoming(notification)
        
    def _NH_SIPSessionDidStart(self, notification):
        """Handle session start with AI integration."""
        session = notification.sender
        session_id = id(session)
        
        self.output.put(f"🤖 AI: Session started, setting up audio processing\n")
        
        # Call the original handler
        super()._NH_SIPSessionDidStart(notification)
        
        # Start audio processing for this session
        if session_id in self.active_ai_calls:
            self._start_audio_processing(session_id)
            
    def _NH_SIPSessionDidEnd(self, notification):
        """Handle session end with AI cleanup."""
        session = notification.sender
        session_id = id(session)
        
        # End AI call session
        if session_id in self.active_ai_calls:
            call_id = self.active_ai_calls[session_id]
            try:
                self._end_ai_call(call_id)
                self.output.put(f"🤖 AI: Ended call session {call_id}\n")
            except Exception as e:
                self.output.put(f"🤖 AI: Error ending call session: {e}\n")
                
            # Clean up
            del self.active_ai_calls[session_id]
            if session_id in self.audio_buffers:
                del self.audio_buffers[session_id]
                
        # Call the original handler
        super()._NH_SIPSessionDidEnd(notification)
        
    def _start_ai_call(self, caller_id):
        """Start an AI call session via HTTP API."""
        try:
            import requests
            response = requests.post(
                f"{self.ai_backend_url}/api/calls/start",
                json={"caller_id": caller_id},
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                return data.get("call_id")
            else:
                self.output.put(f"🤖 AI: Backend error: {response.status_code}\n")
                return None
        except Exception as e:
            self.output.put(f"🤖 AI: Connection error: {e}\n")
            return None
            
    def _end_ai_call(self, call_id):
        """End an AI call session via HTTP API."""
        try:
            import requests
            response = requests.post(
                f"{self.ai_backend_url}/api/calls/{call_id}/end",
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            self.output.put(f"🤖 AI: Error ending call: {e}\n")
            return False
            
    def _start_audio_processing(self, session_id):
        """Start audio processing for a session."""
        def audio_processor():
            """Process audio in a separate thread."""
            while session_id in self.active_ai_calls:
                try:
                    # Check if we have enough audio to process
                    if session_id in self.audio_buffers and len(self.audio_buffers[session_id]) > 32000:
                        audio_data = bytes(self.audio_buffers[session_id])
                        self.audio_buffers[session_id].clear()
                        
                        # Send audio to AI backend
                        call_id = self.active_ai_calls[session_id]
                        response_audio = self._process_audio_with_ai(call_id, audio_data)
                        
                        if response_audio:
                            # TODO: Play response audio back to caller
                            # This would require integration with the SIP stream
                            self.output.put(f"🤖 AI: Generated response audio ({len(response_audio)} bytes)\n")
                            
                    time.sleep(0.5)  # Check every 500ms
                except Exception as e:
                    self.output.put(f"🤖 AI: Audio processing error: {e}\n")
                    break
                    
        # Start audio processing thread
        thread = threading.Thread(target=audio_processor, daemon=True)
        thread.start()
        
    def _process_audio_with_ai(self, call_id, audio_data):
        """Process audio through the AI backend."""
        try:
            import requests
            
            # Send audio data to AI backend
            files = {'audio': ('audio.wav', audio_data, 'audio/wav')}
            response = requests.post(
                f"{self.ai_backend_url}/api/calls/{call_id}/process_audio",
                files=files,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.content  # Response audio
            else:
                self.output.put(f"🤖 AI: Audio processing error: {response.status_code}\n")
                return None
                
        except Exception as e:
            self.output.put(f"🤖 AI: Audio processing error: {e}\n")
            return None


# Create a simple wrapper class that can be imported
class SIPAudioSession(AISIPAudioApplication):
    """Wrapper class for compatibility."""
    pass


def main():
    """Main entry point."""
    import sys
    from optparse import OptionParser
    
    parser = OptionParser()
    parser.add_option("-a", "--account", type="string", dest="account", help="The account name to use.")
    parser.add_option("-c", "--config-directory", type="string", dest="config_directory", help="The configuration directory to use.")
    parser.add_option("-s", "--trace-sip", action="store_true", dest="trace_sip", default=False, help="Dump the raw contents of incoming and outgoing SIP messages.")
    parser.add_option("-j", "--trace-pjsip", action="store_true", dest="trace_pjsip", default=False, help="Print PJSIP logging output.")
    parser.add_option("-n", "--trace-notifications", action="store_true", dest="trace_notifications", default=False, help="Print all notifications.")
    parser.add_option("--disable-sound", action="store_true", dest="disable_sound", default=False, help="Disable sound input/output.")
    parser.add_option("--auto-reconnect", action="store_true", dest="auto_reconnect", default=False, help="Auto reconnect on session failure.")
    parser.add_option("--auto-record", action="store_true", dest="auto_record", default=False, help="Auto record calls.")
    parser.add_option("--batch-mode", action="store_true", dest="batch_mode", default=False, help="Run in batch mode.")
    parser.add_option("--enable-video", action="store_true", dest="enable_video", default=False, help="Enable video.")
    parser.add_option("--enable-default-devices", action="store_true", dest="enable_default_devices", default=False, help="Enable default audio devices.")
    parser.add_option("--disable-ringtone", action="store_true", dest="disable_ringtone", default=False, help="Disable ringtone.")
    parser.add_option("--disable-hanguptone", action="store_true", dest="disable_hanguptone", default=False, help="Disable hangup tone.")
    parser.add_option("--enable-playback", action="store_true", dest="enable_playback", default=False, help="Enable playback.")
    parser.add_option("--playback-dir", type="string", dest="playback_dir", help="Playback directory.")
    parser.add_option("--spool-dir", type="string", dest="spool_dir", help="Spool directory.")
    parser.add_option("--daemonize", action="store_true", dest="daemonize", default=False, help="Daemonize.")
    parser.add_option("--log-register", action="store_true", dest="log_register", default=False, help="Log register.")
    parser.add_option("--play-failure-code", action="store_true", dest="play_failure_code", default=False, help="Play failure code.")
    
    options, args = parser.parse_args()
    
    print("🚀 Starting AI-integrated SIP Client")
    print("=" * 50)
    print("🤖 This client will:")
    print("   • Register with your SIP provider")
    print("   • Handle incoming calls")
    print("   • Send audio to AI backend for processing")
    print("   • Play AI responses back to callers")
    print()
    print("📋 Make sure the AI backend is running:")
    print("   python ai_backend.py")
    print()
    
    try:
        application = AISIPAudioApplication()
        application.start(None, options)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down AI SIP client...")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
