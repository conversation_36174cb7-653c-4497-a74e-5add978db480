#!/usr/bin/env python3
"""
Test script to verify AI backend and SIP client integration.
"""

import requests
import time
import sys


def test_ai_backend():
    """Test if AI backend is running and responsive."""
    print("🧪 Testing AI Backend...")
    
    try:
        # Test health endpoint
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ AI Backend health check: OK")
            health_data = response.json()
            print(f"   Services: {health_data.get('services', {})}")
        else:
            print(f"❌ AI Backend health check failed: {response.status_code}")
            return False
            
        # Test status endpoint
        response = requests.get("http://localhost:8000/api/status", timeout=5)
        if response.status_code == 200:
            print("✅ AI Backend status: OK")
            status_data = response.json()
            print(f"   Active calls: {status_data.get('active_calls_count', 0)}")
        else:
            print(f"❌ AI Backend status failed: {response.status_code}")
            return False
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ AI Backend not reachable at http://localhost:8000")
        print("   Make sure to run: python ai_backend.py")
        return False
    except Exception as e:
        print(f"❌ AI Backend test error: {e}")
        return False


def test_call_api():
    """Test the call management API."""
    print("\n🧪 Testing Call Management API...")
    
    try:
        # Test starting a call
        response = requests.post(
            "http://localhost:8000/api/calls/start",
            json={"caller_id": "<EMAIL>"},
            timeout=5
        )
        
        if response.status_code == 200:
            call_data = response.json()
            call_id = call_data.get("call_id")
            print(f"✅ Call start API: OK (call_id: {call_id})")
            
            # Test getting call status
            response = requests.get(f"http://localhost:8000/api/calls/{call_id}", timeout=5)
            if response.status_code == 200:
                print("✅ Call status API: OK")
                call_status = response.json()
                print(f"   Caller: {call_status.get('caller_id')}")
                print(f"   Active: {call_status.get('is_active')}")
            else:
                print(f"❌ Call status API failed: {response.status_code}")
                
            # Test ending the call
            response = requests.post(f"http://localhost:8000/api/calls/{call_id}/end", timeout=5)
            if response.status_code == 200:
                print("✅ Call end API: OK")
            else:
                print(f"❌ Call end API failed: {response.status_code}")
                
            return True
        else:
            print(f"❌ Call start API failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Call API test error: {e}")
        return False


def test_audio_processing():
    """Test the audio processing API."""
    print("\n🧪 Testing Audio Processing API...")
    
    try:
        # First start a call
        response = requests.post(
            "http://localhost:8000/api/calls/start",
            json={"caller_id": "<EMAIL>"},
            timeout=5
        )
        
        if response.status_code != 200:
            print("❌ Could not start test call for audio processing")
            return False
            
        call_id = response.json().get("call_id")
        
        # Create dummy audio data (silence)
        dummy_audio = b'\x00' * 16000  # 1 second of silence at 16kHz
        
        # Test audio processing
        files = {'audio': ('test.wav', dummy_audio, 'audio/wav')}
        response = requests.post(
            f"http://localhost:8000/api/calls/{call_id}/process_audio",
            files=files,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Audio processing API: OK")
            print(f"   Response audio size: {len(response.content)} bytes")
        else:
            print(f"❌ Audio processing API failed: {response.status_code}")
            
        # Clean up - end the test call
        requests.post(f"http://localhost:8000/api/calls/{call_id}/end", timeout=5)
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Audio processing test error: {e}")
        return False


def main():
    """Run all integration tests."""
    print("🚀 AI SIP Call Agent Integration Test")
    print("=" * 50)
    
    # Test AI backend
    if not test_ai_backend():
        print("\n❌ AI Backend tests failed!")
        print("Make sure to start the AI backend first:")
        print("   python ai_backend.py")
        sys.exit(1)
        
    # Test call management API
    if not test_call_api():
        print("\n❌ Call management API tests failed!")
        sys.exit(1)
        
    # Test audio processing API
    if not test_audio_processing():
        print("\n❌ Audio processing API tests failed!")
        sys.exit(1)
        
    print("\n🎉 All integration tests passed!")
    print("\n📋 Next steps:")
    print("1. ✅ AI Backend is running and healthy")
    print("2. ✅ Call management APIs work")
    print("3. ✅ Audio processing APIs work")
    print("4. 🚀 Ready to start AI SIP client:")
    print("   python ai_sip_client.py")
    print("\n🔗 Integration is ready!")


if __name__ == "__main__":
    main()
