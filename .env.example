# AI SIP Call Agent Environment Variables

# External Service URLs (override config.yaml if needed)
WHISPER_BASE_URL=http://***************:8080
KOKORO_TTS_BASE_URL=http://***************:8081
LITELLM_BASE_URL=https://litellm.xn--8pr.xyz/
LITELLM_API_KEY=sk-1337

# Web Interface
WEB_HOST=0.0.0.0
WEB_PORT=8000
WEB_DEBUG=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/call_agent.log

# Storage Paths
CONVERSATIONS_DIR=./conversations
TRANSCRIPTS_DIR=./transcripts
AUDIO_CACHE_DIR=./audio_cache
RECORDINGS_DIR=./recordings

# SIP Configuration (optional overrides)
SIP_ACCOUNT=
SIP_AUTO_ANSWER=true
SIP_AUTO_ANSWER_INTERVAL=2

# Audio Processing
AUDIO_CHUNK_SIZE=1024
AUDIO_SAMPLE_RATE=16000
SILENCE_THRESHOLD=0.01
SILENCE_DURATION=2.0
MAX_RECORDING_DURATION=30.0

# Agent Behavior
AGENT_NAME="AI Assistant"
AGENT_PERSONALITY="helpful and professional"
MAX_CONVERSATION_TURNS=50
CONVERSATION_TIMEOUT=300
